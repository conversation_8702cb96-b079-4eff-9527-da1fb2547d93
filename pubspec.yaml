name: acount
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Database
  sqflite: ^2.3.0
  path: ^1.8.3

  # Charts and reports
  fl_chart: ^0.66.0

  # Date handling
  intl: ^0.19.0

  # Share functionality
  share_plus: ^7.2.1

  # URL launcher for WhatsApp and SMS
  url_launcher: ^6.2.2

  # Country code picker for phone numbers
  country_code_picker: ^3.0.0

  # File handling for exports
  path_provider: ^2.1.1

  # PDF generation for reports
  pdf: ^3.10.7

  # Barcode generation for PDFs
  barcode: ^2.2.4

  # Excel export
  excel: ^4.0.1

  # Cryptography for password hashing
  crypto: ^3.0.3

  # State management
  provider: ^6.1.1

  # Shared preferences for settings
  shared_preferences: ^2.2.2

  # Google Fonts for Arabic support
  google_fonts: ^6.1.0

  # File picker for attachments
  file_picker: ^8.0.0+1

  # Image picker for photos
  image_picker: ^1.1.2

  # Permission handler for file access
  permission_handler: ^11.3.1

  # Biometric authentication
  local_auth: ^2.1.6

  # Password hashing
  bcrypt: ^1.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts for Arabic support
  fonts:
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
