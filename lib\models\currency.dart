class Currency {
  final String id;
  final String name;
  final String symbol;
  final String code;
  final bool isDefault;
  final DateTime createdAt;

  Currency({
    required this.id,
    required this.name,
    required this.symbol,
    required this.code,
    this.isDefault = false,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'code': code,
      'isDefault': isDefault ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  // Create from Map (database)
  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      symbol: map['symbol'] ?? '',
      code: map['code'] ?? '',
      isDefault: (map['isDefault'] ?? 0) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'code': code,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      symbol: json['symbol'] ?? '',
      code: json['code'] ?? '',
      isDefault: json['isDefault'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Copy with modifications
  Currency copyWith({
    String? id,
    String? name,
    String? symbol,
    String? code,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return Currency(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      code: code ?? this.code,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Format amount with currency
  String formatAmount(double amount) {
    return '${amount.toStringAsFixed(2)} $symbol';
  }

  // Format amount with currency (no decimals for whole numbers)
  String formatAmountClean(double amount) {
    if (amount == amount.roundToDouble()) {
      return '${amount.toStringAsFixed(0)} $symbol';
    }
    return '${amount.toStringAsFixed(2)} $symbol';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Currency && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Currency(id: $id, name: $name, symbol: $symbol, code: $code, isDefault: $isDefault)';
  }

  // Validation methods
  bool get isValid {
    return id.isNotEmpty &&
        name.isNotEmpty &&
        symbol.isNotEmpty &&
        code.isNotEmpty;
  }

  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'اسم العملة مطلوب';
    }
    if (name.trim().length < 2) {
      return 'اسم العملة يجب أن يكون أكثر من حرف واحد';
    }
    if (name.trim().length > 50) {
      return 'اسم العملة طويل جداً';
    }
    return null;
  }

  static String? validateSymbol(String? symbol) {
    if (symbol == null || symbol.trim().isEmpty) {
      return 'رمز العملة مطلوب';
    }
    if (symbol.trim().length > 5) {
      return 'رمز العملة طويل جداً';
    }
    return null;
  }

  static String? validateCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return 'كود العملة مطلوب';
    }
    if (code.trim().length < 2 || code.trim().length > 5) {
      return 'كود العملة يجب أن يكون بين 2-5 أحرف';
    }
    // Check if code contains only letters and numbers
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(code.trim())) {
      return 'كود العملة يجب أن يحتوي على أحرف وأرقام فقط';
    }
    return null;
  }

  // Generate unique ID
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Default currencies for initial setup
  static List<Currency> getDefaultCurrencies() {
    return [
      Currency(
        id: generateId(),
        name: 'الريال السعودي',
        symbol: 'ر.س',
        code: 'SAR',
        isDefault: true,
      ),
      Currency(
        id: generateId(),
        name: 'الدولار الأمريكي',
        symbol: '\$',
        code: 'USD',
      ),
      Currency(id: generateId(), name: 'اليورو', symbol: '€', code: 'EUR'),
    ];
  }
}
