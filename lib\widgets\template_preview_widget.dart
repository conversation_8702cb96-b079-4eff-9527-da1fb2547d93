import 'package:flutter/material.dart';
import '../models/report_template.dart';

class TemplatePreviewWidget extends StatelessWidget {
  final ReportTemplate template;
  final bool isSelected;

  const TemplatePreviewWidget({
    super.key,
    required this.template,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // Header preview
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: _getTemplateGradient(template),
                ),
                child: <PERSON>ack(
                  children: [
                    // Preview content
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title bar
                          Container(
                            height: 8,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          const SizedBox(height: 6),
                          // Subtitle
                          Container(
                            height: 5,
                            width: 80,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          const Spacer(),
                          // Bottom elements
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 3,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.6),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Container(
                                  height: 3,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.6),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Selection indicator
                    if (isSelected)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.check_rounded,
                            color: Colors.blue,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Content preview
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                color: Color(template.backgroundColor),
                padding: const EdgeInsets.all(8),
                child: Column(
                  children: [
                    // Summary section
                    Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(
                          template.accentColor,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Color(
                            template.primaryColor,
                          ).withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 4,
                            margin: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Color(template.accentColor),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 2,
                              decoration: BoxDecoration(
                                color: Color(
                                  template.textColor,
                                ).withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(1),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                      ),
                    ),

                    const SizedBox(height: 6),

                    // Table preview
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Color(
                              template.primaryColor,
                            ).withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          children: [
                            // Table header
                            Container(
                              height: 12,
                              decoration: BoxDecoration(
                                gradient:
                                    template.hasGradient
                                        ? LinearGradient(
                                          colors: [
                                            Color(template.gradientColors[0]),
                                            Color(template.gradientColors[1]),
                                          ],
                                        )
                                        : null,
                                color:
                                    template.hasGradient
                                        ? null
                                        : Color(template.primaryColor),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ),
                            // Table rows
                            Expanded(
                              child: Column(
                                children: List.generate(
                                  3,
                                  (index) => Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color:
                                            index % 2 == 0
                                                ? Color(
                                                  template.backgroundColor,
                                                )
                                                : Color(
                                                  template.primaryColor,
                                                ).withValues(alpha: 0.05),
                                      ),
                                      child: Row(
                                        children: List.generate(
                                          3,
                                          (colIndex) => Expanded(
                                            child: Container(
                                              margin: const EdgeInsets.all(1),
                                              height: 2,
                                              decoration: BoxDecoration(
                                                color: Color(
                                                  template.textColor,
                                                ).withValues(alpha: 0.2),
                                                borderRadius:
                                                    BorderRadius.circular(1),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  LinearGradient _getTemplateGradient(ReportTemplate template) {
    if (template.hasGradient) {
      return LinearGradient(
        colors: [
          Color(template.gradientColors[0]),
          Color(template.gradientColors[1]),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    } else {
      return LinearGradient(
        colors: [Color(template.primaryColor), Color(template.secondaryColor)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
  }
}
