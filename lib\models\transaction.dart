class Transaction {
  final String id;
  final String accountId;
  final String description;
  final double amount;
  final DateTime date;
  final TransactionType type;
  final String? notes;
  final String? currencyCode; // رمز العملة
  final String? currencySymbol; // رمز العملة
  final String? attachmentPath; // مسار المرفق (صورة أو مستند)
  final String? attachmentType; // نوع المرفق (image, document)
  final String? attachmentName; // اسم المرفق الأصلي

  Transaction({
    required this.id,
    required this.accountId,
    required this.description,
    required this.amount,
    required this.date,
    required this.type,
    this.notes,
    this.currencyCode,
    this.currencySymbol,
    this.attachmentPath,
    this.attachmentType,
    this.attachmentName,
  });

  Transaction copyWith({
    String? id,
    String? accountId,
    String? description,
    double? amount,
    DateTime? date,
    TransactionType? type,
    String? notes,
    String? currencyCode,
    String? currencySymbol,
    String? attachmentPath,
    String? attachmentType,
    String? attachmentName,
  }) {
    return Transaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      attachmentType: attachmentType ?? this.attachmentType,
      attachmentName: attachmentName ?? this.attachmentName,
    );
  }

  // Convert to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'description': description,
      'amount': amount,
      'date': date.millisecondsSinceEpoch,
      'type': type.index,
      'notes': notes,
      'currencyCode': currencyCode,
      'currencySymbol': currencySymbol,
      'attachmentPath': attachmentPath,
      'attachmentType': attachmentType,
      'attachmentName': attachmentName,
    };
  }

  // Create from Map (database)
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      accountId: map['accountId'],
      description: map['description'],
      amount: map['amount'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      type: TransactionType.values[map['type']],
      notes: map['notes'],
      currencyCode: map['currencyCode'],
      currencySymbol: map['currencySymbol'],
      attachmentPath: map['attachmentPath'],
      attachmentType: map['attachmentType'],
      attachmentName: map['attachmentName'],
    );
  }

  // Format amount with thousands separator and currency
  String get formattedAmount {
    final formattedNumber = amount
        .toStringAsFixed(2)
        .replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );

    // Use saved currency symbol or default to Saudi Riyal
    final symbol = currencySymbol ?? 'ر.س';
    return '$formattedNumber $symbol';
  }

  // Format amount with specific currency symbol
  String formattedAmountWithCurrency(String currencySymbol) {
    final formattedNumber = amount
        .toStringAsFixed(2)
        .replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
    return '$formattedNumber $currencySymbol';
  }

  // Get formatted date
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Get formatted time
  String get formattedTime {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  // التحقق من وجود مرفق
  bool get hasAttachment {
    return attachmentPath != null && attachmentPath!.isNotEmpty;
  }

  // التحقق من نوع المرفق
  bool get isImageAttachment {
    return attachmentType == 'image';
  }

  bool get isDocumentAttachment {
    return attachmentType == 'document';
  }

  // الحصول على اسم المرفق للعرض
  String get displayAttachmentName {
    if (attachmentName != null && attachmentName!.isNotEmpty) {
      return attachmentName!;
    }
    if (attachmentPath != null && attachmentPath!.isNotEmpty) {
      return attachmentPath!.split('/').last;
    }
    return 'مرفق';
  }

  // الحصول على أيقونة المرفق
  String get attachmentIcon {
    if (isImageAttachment) {
      return '🖼️';
    } else if (isDocumentAttachment) {
      return '📄';
    }
    return '📎';
  }
}

enum TransactionType {
  debit, // مدين (خصم)
  credit, // دائن (إضافة)
}

extension TransactionTypeExtension on TransactionType {
  String get arabicName {
    switch (this) {
      case TransactionType.debit:
        return 'مدين';
      case TransactionType.credit:
        return 'دائن';
    }
  }

  bool get isPositive {
    return this == TransactionType.credit;
  }
}
