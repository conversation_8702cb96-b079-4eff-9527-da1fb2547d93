import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';

import '../models/report_template.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import 'arabic_font_service.dart';
import 'barcode_service.dart';

class EnhancedTemplateHelper {
  static final ArabicFontService _fontService = ArabicFontService();

  // Initialize fonts
  static Future<void> initializeFonts() async {
    await _fontService.loadArabicFonts();
  }

  // Helper function to get contrasting text color
  static PdfColor _getContrastingTextColor(PdfColor backgroundColor) {
    final brightness =
        (backgroundColor.red * 299 +
            backgroundColor.green * 587 +
            backgroundColor.blue * 114) /
        1000;
    return brightness > 0.5 ? PdfColors.black : PdfColors.white;
  }

  // Format date in Arabic style
  static String formatArabicDate(String date) {
    try {
      if (date.contains('/')) {
        final parts = date.split(' ');
        if (parts.isNotEmpty) {
          final datePart = parts[0];
          final timePart = parts.length > 1 ? parts[1] : '';
          return 'التاريخ: $datePart${timePart.isNotEmpty ? ' | الوقت: $timePart' : ''}';
        }
      }
      return date;
    } catch (e) {
      return date;
    }
  }

  // Create stunning header with decorative elements
  static pw.Widget createStunningHeader({
    required ReportTemplate template,
    required String title,
    required String subtitle,
    String? date,
    Account? account,
    Map<String, dynamic>? reportData,
  }) {
    final textColor = _getContrastingTextColor(template.pdfPrimaryColor);
    final subtitleColor =
        textColor == PdfColors.white
            ? PdfColors.white.shade(0.85)
            : PdfColors.black.shade(0.65);

    return pw.Container(
      width: double.infinity,
      margin: const pw.EdgeInsets.only(bottom: 12),
      decoration: pw.BoxDecoration(
        gradient:
            template.hasGradient
                ? pw.LinearGradient(
                  colors: [
                    template.pdfGradientColors[0],
                    template.pdfGradientColors[1],
                    template.pdfGradientColors[0].shade(0.8),
                  ],
                  begin: pw.Alignment.topLeft,
                  end: pw.Alignment.bottomRight,
                  stops: [0.0, 0.6, 1.0],
                )
                : pw.LinearGradient(
                  colors: [
                    template.pdfPrimaryColor,
                    template.pdfPrimaryColor.shade(0.8),
                    template.pdfPrimaryColor.shade(0.9),
                  ],
                  begin: pw.Alignment.topLeft,
                  end: pw.Alignment.bottomRight,
                ),
        borderRadius: pw.BorderRadius.circular(24),
        boxShadow: [
          pw.BoxShadow(
            color: template.pdfPrimaryColor.shade(0.4),
            offset: const PdfPoint(0, 10),
            blurRadius: 20,
          ),
          pw.BoxShadow(
            color: PdfColors.black.shade(0.1),
            offset: const PdfPoint(0, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: pw.Stack(
        children: [
          // Decorative background circles
          pw.Positioned(
            top: -25,
            right: -25,
            child: pw.Container(
              width: 140,
              height: 140,
              decoration: pw.BoxDecoration(
                color:
                    textColor == PdfColors.white
                        ? PdfColors.white.shade(0.08)
                        : PdfColors.black.shade(0.04),
                borderRadius: pw.BorderRadius.circular(70),
              ),
            ),
          ),
          pw.Positioned(
            bottom: -35,
            left: -35,
            child: pw.Container(
              width: 100,
              height: 100,
              decoration: pw.BoxDecoration(
                color:
                    textColor == PdfColors.white
                        ? PdfColors.white.shade(0.06)
                        : PdfColors.black.shade(0.03),
                borderRadius: pw.BorderRadius.circular(50),
              ),
            ),
          ),

          // Main content
          pw.Padding(
            padding: const pw.EdgeInsets.all(32),
            child: pw.Row(
              children: [
                // Header information
                pw.Expanded(
                  flex: 3,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // Decorative top bar
                      pw.Row(
                        children: [
                          pw.Container(
                            width: 80,
                            height: 5,
                            decoration: pw.BoxDecoration(
                              gradient: pw.LinearGradient(
                                colors: [
                                  textColor == PdfColors.white
                                      ? PdfColors.white.shade(0.9)
                                      : PdfColors.black.shade(0.7),
                                  textColor == PdfColors.white
                                      ? PdfColors.white.shade(0.5)
                                      : PdfColors.black.shade(0.3),
                                ],
                              ),
                              borderRadius: pw.BorderRadius.circular(3),
                            ),
                          ),
                          pw.SizedBox(width: 8),
                          pw.Container(
                            width: 20,
                            height: 5,
                            decoration: pw.BoxDecoration(
                              color:
                                  textColor == PdfColors.white
                                      ? PdfColors.white.shade(0.6)
                                      : PdfColors.black.shade(0.4),
                              borderRadius: pw.BorderRadius.circular(3),
                            ),
                          ),
                        ],
                      ),

                      pw.SizedBox(height: 16),

                      // Main title with enhanced styling
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: pw.BoxDecoration(
                          color:
                              textColor == PdfColors.white
                                  ? PdfColors.white.shade(0.25)
                                  : PdfColors.black.shade(0.15),
                          borderRadius: pw.BorderRadius.circular(20),
                          border: pw.Border.all(
                            color:
                                textColor == PdfColors.white
                                    ? PdfColors.white.shade(0.5)
                                    : PdfColors.black.shade(0.3),
                            width: 2,
                          ),
                          boxShadow: [
                            pw.BoxShadow(
                              color:
                                  textColor == PdfColors.white
                                      ? PdfColors.white.shade(0.15)
                                      : PdfColors.black.shade(0.1),
                              offset: const PdfPoint(0, 4),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: pw.Row(
                          mainAxisSize: pw.MainAxisSize.min,
                          children: [
                            // Icon container
                            pw.Container(
                              padding: const pw.EdgeInsets.all(8),
                              decoration: pw.BoxDecoration(
                                gradient: pw.LinearGradient(
                                  colors: [
                                    textColor == PdfColors.white
                                        ? PdfColors.white.shade(0.4)
                                        : PdfColors.black.shade(0.25),
                                    textColor == PdfColors.white
                                        ? PdfColors.white.shade(0.2)
                                        : PdfColors.black.shade(0.15),
                                  ],
                                ),
                                borderRadius: pw.BorderRadius.circular(12),
                              ),
                              child: pw.Text(
                                '📊',
                                style: pw.TextStyle(fontSize: 20),
                              ),
                            ),
                            pw.SizedBox(width: 16),
                            pw.Flexible(
                              child: pw.Text(
                                title,
                                style: _fontService.getArabicHeaderStyle(
                                  fontSize: 26,
                                  color: textColor,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            ),
                          ],
                        ),
                      ),

                      pw.SizedBox(height: 20),

                      // Subtitle with icon
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: pw.BoxDecoration(
                          color:
                              textColor == PdfColors.white
                                  ? PdfColors.white.shade(0.15)
                                  : PdfColors.black.shade(0.1),
                          borderRadius: pw.BorderRadius.circular(16),
                          border: pw.Border.all(
                            color:
                                textColor == PdfColors.white
                                    ? PdfColors.white.shade(0.3)
                                    : PdfColors.black.shade(0.2),
                            width: 1,
                          ),
                        ),
                        child: pw.Row(
                          children: [
                            pw.Text('📋', style: pw.TextStyle(fontSize: 16)),
                            pw.SizedBox(width: 12),
                            pw.Expanded(
                              child: pw.Text(
                                subtitle,
                                style: _fontService.getArabicTextStyle(
                                  fontSize: 18,
                                  color: subtitleColor,
                                  bold: true,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            ),
                          ],
                        ),
                      ),

                      if (date != null) ...[
                        pw.SizedBox(height: 16),
                        // Enhanced date display
                        pw.Container(
                          padding: const pw.EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: pw.BoxDecoration(
                            gradient: pw.LinearGradient(
                              colors: [
                                textColor == PdfColors.white
                                    ? PdfColors.white.shade(0.2)
                                    : PdfColors.black.shade(0.12),
                                textColor == PdfColors.white
                                    ? PdfColors.white.shade(0.1)
                                    : PdfColors.black.shade(0.08),
                              ],
                            ),
                            borderRadius: pw.BorderRadius.circular(14),
                            border: pw.Border.all(
                              color:
                                  textColor == PdfColors.white
                                      ? PdfColors.white.shade(0.4)
                                      : PdfColors.black.shade(0.25),
                              width: 1.5,
                            ),
                          ),
                          child: pw.Row(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Container(
                                padding: const pw.EdgeInsets.all(6),
                                decoration: pw.BoxDecoration(
                                  color:
                                      textColor == PdfColors.white
                                          ? PdfColors.white.shade(0.3)
                                          : PdfColors.black.shade(0.2),
                                  borderRadius: pw.BorderRadius.circular(8),
                                ),
                                child: pw.Text(
                                  '📅',
                                  style: pw.TextStyle(fontSize: 14),
                                ),
                              ),
                              pw.SizedBox(width: 12),
                              pw.Text(
                                formatArabicDate(date),
                                style: _fontService.getArabicTextStyle(
                                  fontSize: 15,
                                  color: subtitleColor,
                                  bold: true,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                pw.SizedBox(width: 28),

                // Barcode and visual elements
                pw.Column(
                  children: [
                    // Main decorative icon
                    pw.Container(
                      padding: const pw.EdgeInsets.all(24),
                      decoration: pw.BoxDecoration(
                        gradient: pw.LinearGradient(
                          colors: [
                            textColor == PdfColors.white
                                ? PdfColors.white.shade(0.35)
                                : PdfColors.black.shade(0.25),
                            textColor == PdfColors.white
                                ? PdfColors.white.shade(0.2)
                                : PdfColors.black.shade(0.15),
                            textColor == PdfColors.white
                                ? PdfColors.white.shade(0.1)
                                : PdfColors.black.shade(0.08),
                          ],
                          begin: pw.Alignment.topLeft,
                          end: pw.Alignment.bottomRight,
                        ),
                        borderRadius: pw.BorderRadius.circular(24),
                        border: pw.Border.all(
                          color:
                              textColor == PdfColors.white
                                  ? PdfColors.white.shade(0.6)
                                  : PdfColors.black.shade(0.4),
                          width: 3,
                        ),
                        boxShadow: [
                          pw.BoxShadow(
                            color:
                                textColor == PdfColors.white
                                    ? PdfColors.white.shade(0.25)
                                    : PdfColors.black.shade(0.2),
                            offset: const PdfPoint(0, 6),
                            blurRadius: 12,
                          ),
                        ],
                      ),
                      child: pw.Text(
                        account != null
                            ? '👤'
                            : reportData != null
                            ? '📊'
                            : '📄',
                        style: pw.TextStyle(fontSize: 40),
                      ),
                    ),

                    pw.SizedBox(height: 20),

                    // Enhanced barcode container
                    if (account != null || reportData != null)
                      pw.Container(
                        padding: const pw.EdgeInsets.all(16),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.white,
                          borderRadius: pw.BorderRadius.circular(16),
                          border: pw.Border.all(
                            color: template.pdfPrimaryColor.shade(0.4),
                            width: 3,
                          ),
                          boxShadow: [
                            pw.BoxShadow(
                              color: template.pdfPrimaryColor.shade(0.3),
                              blurRadius: 12,
                              offset: const PdfPoint(0, 6),
                            ),
                            pw.BoxShadow(
                              color: PdfColors.black.shade(0.1),
                              blurRadius: 4,
                              offset: const PdfPoint(0, 2),
                            ),
                          ],
                        ),
                        child: pw.Column(
                          children: [
                            if (account != null)
                              BarcodeService.createAccountBarcode(account)
                            else if (reportData != null)
                              BarcodeService.createReportBarcode(
                                title,
                                reportData,
                              ),
                            pw.SizedBox(height: 8),
                            pw.Container(
                              padding: const pw.EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              decoration: pw.BoxDecoration(
                                color: template.pdfPrimaryColor.shade(0.1),
                                borderRadius: pw.BorderRadius.circular(8),
                              ),
                              child: pw.Text(
                                'رمز الاستجابة السريعة',
                                style: _fontService.getArabicTextStyle(
                                  fontSize: 9,
                                  color: template.pdfPrimaryColor,
                                  bold: true,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Create enhanced table with better styling
  static pw.Widget createEnhancedTable({
    required ReportTemplate template,
    required List<String> headers,
    required List<List<String>> data,
    List<bool>? boldColumns,
    List<Transaction>? transactions,
    List<Account>? accounts,
    bool showBarcodes = false,
  }) {
    final headerTextColor = _getContrastingTextColor(template.pdfPrimaryColor);

    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(16),
        boxShadow: [
          pw.BoxShadow(
            color: template.pdfPrimaryColor.shade(0.2),
            offset: const PdfPoint(0, 8),
            blurRadius: 16,
          ),
          pw.BoxShadow(
            color: PdfColors.black.shade(0.05),
            offset: const PdfPoint(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Table(
        border: pw.TableBorder.all(
          color: template.pdfPrimaryColor.shade(0.3),
          width: 1,
        ),
        children: [
          // Enhanced header row
          pw.TableRow(
            decoration: pw.BoxDecoration(
              gradient:
                  template.hasGradient
                      ? pw.LinearGradient(
                        colors: [
                          template.pdfGradientColors[0],
                          template.pdfGradientColors[1],
                        ],
                        begin: pw.Alignment.centerLeft,
                        end: pw.Alignment.centerRight,
                      )
                      : pw.LinearGradient(
                        colors: [
                          template.pdfPrimaryColor,
                          template.pdfPrimaryColor.shade(0.8),
                        ],
                        begin: pw.Alignment.centerLeft,
                        end: pw.Alignment.centerRight,
                      ),
            ),
            children: [
              if (showBarcodes)
                pw.Container(
                  padding: const pw.EdgeInsets.all(16),
                  child: pw.Center(
                    child: pw.Text(
                      'الرمز',
                      style: _fontService.getArabicTextStyle(
                        fontSize: 13,
                        bold: true,
                        color: headerTextColor,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                ),
              ...headers.map(
                (header) => pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: pw.Center(
                    child: pw.Text(
                      header,
                      style: _fontService.getArabicTextStyle(
                        fontSize: 13,
                        bold: true,
                        color: headerTextColor,
                      ),
                      textAlign: pw.TextAlign.center,
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Enhanced data rows
          ...data.asMap().entries.map((entry) {
            final index = entry.key;
            final row = entry.value;
            final isEven = index % 2 == 0;
            final transaction =
                transactions != null && index < transactions.length
                    ? transactions[index]
                    : null;
            final account =
                accounts != null && index < accounts.length
                    ? accounts[index]
                    : null;

            return pw.TableRow(
              decoration: pw.BoxDecoration(
                color:
                    isEven
                        ? template.pdfBackgroundColor
                        : template.pdfPrimaryColor.shade(0.03),
                border: pw.Border(
                  bottom: pw.BorderSide(
                    color: template.pdfPrimaryColor.shade(0.15),
                    width: 0.5,
                  ),
                ),
              ),
              children: [
                // Barcode column if enabled
                if (showBarcodes)
                  pw.Container(
                    padding: const pw.EdgeInsets.all(12),
                    child: pw.Center(
                      child:
                          (transaction != null && account != null)
                              ? BarcodeService.createTransactionBarcode(
                                transaction,
                                account,
                              )
                              : account != null
                              ? BarcodeService.createAccountBarcode(account)
                              : pw.SizedBox(width: 40, height: 40),
                    ),
                  ),

                // Data columns
                ...row.asMap().entries.map((cellEntry) {
                  final colIndex = cellEntry.key;
                  final cell = cellEntry.value;
                  final isBold =
                      boldColumns != null &&
                      colIndex < boldColumns.length &&
                      boldColumns[colIndex];

                  return pw.Container(
                    padding: const pw.EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                    child: pw.Center(
                      child: pw.Text(
                        cell,
                        style: _fontService.getArabicTextStyle(
                          fontSize: 12,
                          bold: isBold,
                          color: template.pdfTextColor,
                        ),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                  );
                }),
              ],
            );
          }),
        ],
      ),
    );
  }

  // Create enhanced summary section
  static pw.Widget createEnhancedSummary({
    required ReportTemplate template,
    required String title,
    required List<String> summaryItems,
  }) {
    final textColor = _getContrastingTextColor(template.pdfAccentColor);

    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      padding: const pw.EdgeInsets.all(24),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [template.pdfAccentColor, template.pdfAccentColor.shade(0.8)],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(20),
        border: pw.Border.all(
          color: template.pdfPrimaryColor.shade(0.3),
          width: 2,
        ),
        boxShadow: [
          pw.BoxShadow(
            color: template.pdfAccentColor.shade(0.3),
            offset: const PdfPoint(0, 6),
            blurRadius: 12,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Section title with icon
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 12,
            ),
            decoration: pw.BoxDecoration(
              color:
                  textColor == PdfColors.white
                      ? PdfColors.white.shade(0.2)
                      : PdfColors.black.shade(0.15),
              borderRadius: pw.BorderRadius.circular(16),
              border: pw.Border.all(
                color:
                    textColor == PdfColors.white
                        ? PdfColors.white.shade(0.4)
                        : PdfColors.black.shade(0.3),
                width: 1,
              ),
            ),
            child: pw.Row(
              mainAxisSize: pw.MainAxisSize.min,
              children: [
                pw.Text('📈', style: pw.TextStyle(fontSize: 18)),
                pw.SizedBox(width: 12),
                pw.Text(
                  title,
                  style: _fontService.getArabicTextStyle(
                    fontSize: 18,
                    bold: true,
                    color: textColor,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Summary items with enhanced styling
          ...summaryItems.map(
            (item) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 12),
              padding: const pw.EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
              decoration: pw.BoxDecoration(
                color:
                    textColor == PdfColors.white
                        ? PdfColors.white.shade(0.15)
                        : PdfColors.black.shade(0.1),
                borderRadius: pw.BorderRadius.circular(12),
                border: pw.Border.all(
                  color:
                      textColor == PdfColors.white
                          ? PdfColors.white.shade(0.3)
                          : PdfColors.black.shade(0.2),
                  width: 1,
                ),
              ),
              child: pw.Row(
                children: [
                  pw.Container(
                    width: 8,
                    height: 8,
                    decoration: pw.BoxDecoration(
                      gradient: pw.LinearGradient(
                        colors: [
                          textColor == PdfColors.white
                              ? PdfColors.white.shade(0.8)
                              : PdfColors.black.shade(0.6),
                          textColor == PdfColors.white
                              ? PdfColors.white.shade(0.5)
                              : PdfColors.black.shade(0.3),
                        ],
                      ),
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                  ),
                  pw.SizedBox(width: 12),
                  pw.Expanded(
                    child: pw.Text(
                      item,
                      style: _fontService.getArabicTextStyle(
                        fontSize: 14,
                        color: textColor,
                        bold: false,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
