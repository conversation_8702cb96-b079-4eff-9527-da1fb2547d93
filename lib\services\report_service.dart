import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';
import 'arabic_font_service.dart';

class ReportService {
  static final ReportService _instance = ReportService._internal();
  factory ReportService() => _instance;
  ReportService._internal();

  final DatabaseHelper _storage = DatabaseHelper();
  final ArabicFontService _fontService = ArabicFontService();

  // Generate PDF Report for All Accounts
  Future<File> generateAccountsPDFReport() async {
    // Load Arabic fonts first
    await _fontService.loadArabicFonts();

    final pdf = pw.Document();
    final accounts = await _storage.getAllAccounts();
    final now = DateTime.now();
    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // Header
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'تقرير الحسابات الشامل',
                    style: _fontService.getArabicHeaderStyle(
                      fontSize: 24,
                      color: PdfColors.blue900,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التقرير: ${formatter.format(now)}',
                    style: _fontService.getArabicTextStyle(fontSize: 14),
                  ),
                  pw.Text(
                    'عدد الحسابات: ${accounts.length}',
                    style: _fontService.getArabicTextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'ملخص الحسابات',
                    style: _fontService.getArabicHeaderStyle(fontSize: 18),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'إجمالي الحسابات الدائنة: ${accounts.where((a) => a.isPositive).length}',
                        style: _fontService.getArabicTextStyle(),
                      ),
                      pw.Text(
                        'إجمالي الحسابات المدينة: ${accounts.where((a) => !a.isPositive).length}',
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 5),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'إجمالي الأرصدة الدائنة: ${accounts.where((a) => a.isPositive).fold(0.0, (sum, a) => sum + a.balance).toStringAsFixed(2)} ر.س',
                      ),
                      pw.Text(
                        'إجمالي الأرصدة المدينة: ${accounts.where((a) => !a.isPositive).fold(0.0, (sum, a) => sum + a.balance).toStringAsFixed(2)} ر.س',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Accounts Table
            pw.Text(
              'تفاصيل الحسابات',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),

            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(3),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(1.5),
                3: const pw.FlexColumnWidth(1.5),
                4: const pw.FlexColumnWidth(2),
              },
              children: [
                // Header Row
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'اسم الحساب',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'الرصيد',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'النوع',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'المعاملات',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'تاريخ الإنشاء',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                // Data Rows
                ...accounts.map(
                  (account) => pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(account.name),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '${account.formattedBalance} ر.س',
                          style: pw.TextStyle(
                            color:
                                account.isPositive
                                    ? PdfColors.green
                                    : PdfColors.red,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(account.typeInArabic),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(account.transactionCount.toString()),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(account.formattedCreatedDate),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ];
        },
      ),
    );

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/accounts_report_${DateFormat('yyyyMMdd_HHmmss').format(now)}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  // Generate Excel Report for All Accounts
  Future<File> generateAccountsExcelReport() async {
    final excel = Excel.createExcel();
    final sheet = excel['الحسابات'];
    final accounts = await _storage.getAllAccounts();
    final now = DateTime.now();

    // Set RTL direction
    sheet.isRTL = true;

    // Header styling
    final headerStyle = CellStyle(
      backgroundColorHex: ExcelColor.blue,
      fontColorHex: ExcelColor.white,
      bold: true,
      fontSize: 12,
    );

    // Add headers
    final headers = [
      'اسم الحساب',
      'الرصيد',
      'النوع',
      'عدد المعاملات',
      'تاريخ الإنشاء',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = headerStyle;
    }

    // Add data
    for (int i = 0; i < accounts.length; i++) {
      final account = accounts[i];
      final rowIndex = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(account.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue('${account.formattedBalance} ر.س');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = TextCellValue(account.typeInArabic);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = IntCellValue(account.transactionCount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = TextCellValue(account.formattedCreatedDate);

      // Color coding for balance
      final balanceCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex),
      );
      balanceCell.cellStyle = CellStyle(
        fontColorHex: account.isPositive ? ExcelColor.green : ExcelColor.red,
        bold: true,
      );
    }

    // Auto-fit columns
    for (int i = 0; i < headers.length; i++) {
      sheet.setColumnAutoFit(i);
    }

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/accounts_report_${DateFormat('yyyyMMdd_HHmmss').format(now)}.xlsx',
    );
    final bytes = excel.save();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    return file;
  }

  // Generate Account Statement PDF
  Future<File> generateAccountStatementPDF(Account account) async {
    // Load Arabic fonts first
    await _fontService.loadArabicFonts();

    final pdf = pw.Document();
    final transactions = await _storage.getTransactionsByAccount(account.id);
    final now = DateTime.now();
    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // Header
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: account.isPositive ? PdfColors.green50 : PdfColors.red50,
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'كشف حساب',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      color:
                          account.isPositive
                              ? PdfColors.green900
                              : PdfColors.red900,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'اسم الحساب: ${account.name}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'نوع الحساب: ${account.typeInArabic}',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                  pw.Text(
                    'الرصيد الحالي: ${account.formattedBalance} ر.س',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                      color:
                          account.isPositive ? PdfColors.green : PdfColors.red,
                    ),
                  ),
                  pw.Text(
                    'تاريخ التقرير: ${formatter.format(now)}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'ملخص الحساب',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('عدد المعاملات: ${transactions.length}'),
                      pw.Text('تاريخ الإنشاء: ${account.formattedCreatedDate}'),
                    ],
                  ),
                  pw.SizedBox(height: 5),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'إجمالي الدائن: ${transactions.where((t) => t.type.isPositive).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
                      ),
                      pw.Text(
                        'إجمالي المدين: ${transactions.where((t) => !t.type.isPositive).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Transactions Table
            if (transactions.isNotEmpty) ...[
              pw.Text(
                'تفاصيل المعاملات',
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),

              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey300),
                columnWidths: {
                  0: const pw.FlexColumnWidth(2),
                  1: const pw.FlexColumnWidth(3),
                  2: const pw.FlexColumnWidth(2),
                  3: const pw.FlexColumnWidth(1.5),
                  4: const pw.FlexColumnWidth(2),
                },
                children: [
                  // Header Row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey100,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'التاريخ',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'الوصف',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'المبلغ',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'النوع',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'ملاحظات',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  // Data Rows
                  ...transactions.map(
                    (transaction) => pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(transaction.formattedDate),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(transaction.description),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${transaction.formattedAmount} ر.س',
                            style: pw.TextStyle(
                              color:
                                  transaction.type.isPositive
                                      ? PdfColors.green
                                      : PdfColors.red,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            transaction.type.isPositive ? 'دائن' : 'مدين',
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(transaction.notes ?? '-'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ] else ...[
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Center(
                  child: pw.Text(
                    'لا توجد معاملات لهذا الحساب',
                    style: pw.TextStyle(fontSize: 16, color: PdfColors.grey600),
                  ),
                ),
              ),
            ],
          ];
        },
      ),
    );

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/account_statement_${account.name}_${DateFormat('yyyyMMdd_HHmmss').format(now)}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  // Share file
  Future<void> shareFile(File file, String title) async {
    await Share.shareXFiles([XFile(file.path)], text: title);
  }

  // Generate All Reports (PDF + Excel)
  Future<List<File>> generateAllAccountsReports() async {
    final pdfFile = await generateAccountsPDFReport();
    final excelFile = await generateAccountsExcelReport();
    return [pdfFile, excelFile];
  }

  // Generate Account Statement (PDF + Excel)
  Future<List<File>> generateAccountStatementReports(Account account) async {
    final pdfFile = await generateAccountStatementPDF(account);
    return [pdfFile];
  }
}
