import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';
import 'add_amount_screen.dart';
import 'search_screen.dart';
import 'reports_screen.dart';
import 'all_accounts_screen.dart';
import '../widgets/app_drawer.dart';
import '../widgets/theme_toggle_button.dart';
import '../theme/app_theme.dart';
import '../services/responsive_service.dart';
import '../services/app_state_service.dart';

import '../widgets/responsive_layout.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند بدء الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateService>().loadAllData();
    });
  }

  Future<void> _addAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );
    if (result == true) {
      // البيانات ستتحدث تلقائياً عبر AppStateService
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إضافة الحساب بنجاح'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  // بناء شريط التطبيق المبسط والأنيق
  PreferredSizeWidget _buildModernAppBar(
    BuildContext context,
    AppStateService appState,
  ) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.account_balance_wallet_rounded,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'دفتر الحسابات',
            style: TextStyle(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
      leading:
          ResponsiveService.shouldUseDrawer(context)
              ? Builder(
                builder:
                    (context) => IconButton(
                      icon: const Icon(
                        Icons.menu_rounded,
                        color: Colors.black87,
                      ),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
              )
              : null,
      actions: [
        const ThemeToggleButton(),
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.black54),
          onPressed: () => appState.refresh(),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateService>(
      builder: (context, appState, child) {
        final accounts = appState.accounts;
        final isLoading = appState.isLoading;
        final totalCredit = appState.totalPositiveBalance;
        final totalDebit = appState.totalNegativeBalance;

        return ResponsiveBuilder(
          builder: (context, deviceType) {
            return Scaffold(
              backgroundColor: AppTheme.backgroundColor,
              drawer:
                  ResponsiveService.shouldUseDrawer(context)
                      ? const AppDrawer()
                      : null,
              extendBodyBehindAppBar: true,
              appBar: _buildModernAppBar(context, appState),
              body: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors:
                        Theme.of(context).brightness == Brightness.dark
                            ? [
                              AppTheme.darkBackgroundColor,
                              AppTheme.darkSurfaceColor,
                            ]
                            : [
                              const Color(0xFFF8F9FA),
                              const Color(0xFFE9ECEF),
                            ],
                  ),
                ),
                child: SafeArea(
                  child:
                      isLoading
                          ? _buildLoadingState()
                          : accounts.isEmpty
                          ? _buildWelcomeScreen()
                          : _buildMainContent(
                            accounts,
                            totalCredit,
                            totalDebit,
                            appState,
                          ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // بناء شاشة الترحيب للمستخدمين الجدد
  Widget _buildWelcomeScreen() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const SizedBox(height: 40),
          // أيقونة ترحيبية كبيرة
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Icon(
              Icons.account_balance_wallet_rounded,
              size: 80,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 32),
          // نص الترحيب
          Text(
            'مرحباً بك في دفتر الحسابات!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'ابدأ رحلتك المالية بإضافة أول حساب\nوتتبع جميع معاملاتك بسهولة',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.black54, height: 1.5),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          // زر إضافة حساب جديد
          Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _addAccount,
                borderRadius: BorderRadius.circular(16),
                child: const Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_rounded, color: Colors.white, size: 24),
                      SizedBox(width: 8),
                      Text(
                        'إضافة حساب جديد',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 32),
          // بطاقات المميزات
          Row(
            children: [
              Expanded(
                child: _buildFeatureCard(
                  'إدارة سهلة',
                  'تتبع جميع حساباتك في مكان واحد',
                  Icons.dashboard_rounded,
                  AppTheme.successGradient,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFeatureCard(
                  'تقارير مفصلة',
                  'احصل على تقارير شاملة لمعاملاتك',
                  Icons.analytics_rounded,
                  AppTheme.infoGradient,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة ميزة
  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    LinearGradient gradient,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black54,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء المحتوى الرئيسي
  Widget _buildMainContent(
    List<Account> accounts,
    double totalCredit,
    double totalDebit,
    AppStateService appState,
  ) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const SizedBox(height: 20),
          // بطاقة الملخص المالي
          _buildFinancialSummary(totalCredit, totalDebit),
          const SizedBox(height: 20),
          // شريط الإجراءات السريعة
          _buildActionBar(),
          const SizedBox(height: 20),
          // قائمة الحسابات
          _buildAccountsGrid(accounts, appState),
        ],
      ),
    );
  }

  // بناء الملخص المالي الجديد
  Widget _buildFinancialSummary(double totalCredit, double totalDebit) {
    final netBalance = totalCredit - totalDebit;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // الرصيد الصافي
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.account_balance_wallet_rounded,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الرصيد الصافي',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${netBalance.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          // إحصائيات مبسطة
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'لنا',
                  totalCredit,
                  Icons.arrow_upward_rounded,
                  Colors.green.shade400,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'علينا',
                  totalDebit,
                  Icons.arrow_downward_rounded,
                  Colors.red.shade400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة ملخص مبسطة
  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط الإجراءات المبسط
  Widget _buildActionBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              'إضافة حساب',
              Icons.person_add_rounded,
              AppTheme.primaryGradient,
              _addAccount,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              'البحث',
              Icons.search_rounded,
              AppTheme.infoGradient,
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SearchScreen()),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              'التقارير',
              Icons.analytics_rounded,
              AppTheme.purpleGradient,
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ReportsScreen()),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء زر إجراء
  Widget _buildActionButton(
    String title,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(width: 6),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء شبكة الحسابات المحسنة
  Widget _buildAccountsGrid(List<Account> accounts, AppStateService appState) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'حساباتي',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              TextButton.icon(
                onPressed:
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AllAccountsScreen(),
                      ),
                    ),
                icon: const Icon(Icons.arrow_forward_rounded, size: 16),
                label: const Text('عرض الكل'),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: ResponsiveService.isDesktop(context) ? 3 : 2,
              childAspectRatio: 1.1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: accounts.length,
            itemBuilder: (context, index) {
              return _buildModernAccountCard(accounts[index], appState);
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  // بناء بطاقة حساب عصرية
  Widget _buildModernAccountCard(Account account, AppStateService appState) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AccountDetailsScreen(account: account),
              ),
            );
            if (result == true) {
              appState.refresh();
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.person_rounded,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) async {
                        if (value == 'add') {
                          final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      AddAmountScreen(account: account),
                            ),
                          );
                          if (result == true) {
                            appState.refresh();
                          }
                        }
                      },
                      itemBuilder:
                          (context) => [
                            const PopupMenuItem(
                              value: 'add',
                              child: Row(
                                children: [
                                  Icon(Icons.add_rounded, size: 16),
                                  SizedBox(width: 8),
                                  Text('إضافة معاملة'),
                                ],
                              ),
                            ),
                          ],
                      child: const Icon(
                        Icons.more_vert_rounded,
                        color: Colors.black54,
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  account.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  account.formattedBalanceWithCurrency('ر.س'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color:
                        account.balance >= 0
                            ? Colors.green.shade600
                            : Colors.red.shade600,
                  ),
                ),
                const Spacer(),
                Container(
                  width: double.infinity,
                  height: 3,
                  decoration: BoxDecoration(
                    gradient:
                        account.balance >= 0
                            ? AppTheme.successGradient
                            : AppTheme.errorGradient,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الحسابات...',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: AppTheme.textSecondary),
          ),
        ],
      ),
    );
  }
}
