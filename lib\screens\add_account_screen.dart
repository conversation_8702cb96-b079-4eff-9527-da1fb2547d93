import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart' as modern;
import '../theme/app_theme.dart';

class AddAccountScreen extends StatefulWidget {
  const AddAccountScreen({super.key});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  final _balanceController = TextEditingController();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  bool _isPositive = true;
  bool _isLoading = false;
  String _countryCode = '+966'; // Default to Saudi Arabia
  Currency? _selectedCurrency;
  List<Currency> _availableCurrencies = [];

  @override
  void initState() {
    super.initState();
    _loadCurrencies();
  }

  Future<void> _loadCurrencies() async {
    try {
      await _currencyService.addDefaultCurrenciesIfEmpty();
      final currencies = await _currencyService.getAllCurrencies();
      final defaultCurrency = await _currencyService.getDefaultCurrency();

      setState(() {
        _availableCurrencies = currencies;
        _selectedCurrency =
            defaultCurrency ??
            (currencies.isNotEmpty ? currencies.first : null);
      });
    } catch (e) {
      print('Error loading currencies: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  Future<void> _saveAccount() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final balance = double.parse(_balanceController.text);

        // Combine country code with phone number
        String? fullPhoneNumber;
        if (_phoneController.text.trim().isNotEmpty) {
          String phoneNumber = _phoneController.text.trim();
          // Remove any existing country code or + sign
          phoneNumber = phoneNumber.replaceAll(RegExp(r'^(\+|00)?966'), '');
          phoneNumber = phoneNumber.replaceAll(RegExp(r'^\+'), '');
          // Add the selected country code
          fullPhoneNumber = '$_countryCode$phoneNumber';
        }

        final account = Account(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: _nameController.text.trim(),
          phoneNumber: fullPhoneNumber,
          address:
              _addressController.text.trim().isEmpty
                  ? null
                  : _addressController.text.trim(),
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
          balance: balance,
          initialBalance: balance, // حفظ الرصيد الابتدائي
          initialIsPositive: _isPositive, // حفظ نوع الرصيد الابتدائي
          transactionCount: 0,
          isPositive: _isPositive,
          createdAt: DateTime.now(),
          currencyId: _selectedCurrency?.id,
        );

        final success = await context.read<AppStateService>().addAccount(
          account,
        );

        if (mounted) {
          if (success) {
            Navigator.pop(context, true);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إنشاء الحساب "${account.name}" بنجاح'),
                backgroundColor: AppTheme.successColor,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('خطأ في إنشاء الحساب'),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إنشاء الحساب: $e'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppTheme.cardShadow,
          ),
          child: Text(
            'إضافة حساب جديد',
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors:
                Theme.of(context).brightness == Brightness.dark
                    ? [AppTheme.darkBackgroundColor, AppTheme.darkSurfaceColor]
                    : [const Color(0xFFF8F9FA), const Color(0xFFE9ECEF)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: ResponsiveHelper.getResponsivePadding(context),
            child: Column(
              children: [
                const SizedBox(height: 20),
                // Header Card
                ModernCard(
                  gradient: AppTheme.primaryGradient,
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.account_balance_wallet_rounded,
                          color: Colors.white,
                          size: ResponsiveHelper.getResponsiveIconSize(
                            context,
                            24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'حساب جديد',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      12,
                                    ),
                              ),
                            ),
                            Text(
                              'إنشاء حساب جديد',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      18,
                                    ),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'أدخل بيانات الحساب الجديد',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      14,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Form Card
                ModernCard(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'بيانات الحساب',
                          style: TextStyle(
                            color:
                                Theme.of(context).textTheme.titleLarge?.color,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              18,
                            ),
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Account Name Field
                        _buildTextField(
                          controller: _nameController,
                          label: 'اسم الحساب *',
                          hint: 'أدخل اسم الحساب',
                          icon: Icons.person_rounded,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال اسم الحساب';
                            }
                            if (value.trim().length < 2) {
                              return 'يجب أن يكون اسم الحساب أكثر من حرفين';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Phone Number Field with Country Code
                        _buildPhoneField(),

                        const SizedBox(height: 16),

                        // Address Field
                        _buildTextField(
                          controller: _addressController,
                          label: 'العنوان',
                          hint: 'أدخل العنوان (اختياري)',
                          icon: Icons.location_on_rounded,
                          maxLines: 2,
                        ),

                        const SizedBox(height: 16),

                        // Notes Field
                        _buildTextField(
                          controller: _notesController,
                          label: 'ملاحظات',
                          hint: 'أدخل ملاحظات إضافية (اختياري)',
                          icon: Icons.note_rounded,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 16),

                        // Currency Selection
                        _buildCurrencySelector(),

                        const SizedBox(height: 16),

                        // Balance Field
                        _buildTextField(
                          controller: _balanceController,
                          label:
                              'الرصيد الابتدائي (${_selectedCurrency?.symbol ?? 'ر.س'})',
                          hint:
                              'أدخل الرصيد الابتدائي بـ ${_selectedCurrency?.name ?? 'الريال السعودي'}',
                          icon: Icons.attach_money_rounded,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الرصيد';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال مبلغ صحيح';
                            }
                            if (double.parse(value) < 0) {
                              return 'يجب أن يكون المبلغ صفر أو أكبر';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 24),

                        // Account Type Selection
                        Text(
                          'نوع الحساب',
                          style: TextStyle(
                            color:
                                Theme.of(context).textTheme.titleMedium?.color,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14,
                            ),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),

                        Row(
                          children: [
                            Expanded(
                              child: _buildTypeOption(
                                title: 'دائن (لنا)',
                                subtitle: 'مبلغ مستحق لنا',
                                icon: Icons.trending_up_rounded,
                                isSelected: _isPositive,
                                gradient: AppTheme.successGradient,
                                onTap: () => setState(() => _isPositive = true),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTypeOption(
                                title: 'مدين (علينا)',
                                subtitle: 'مبلغ مستحق علينا',
                                icon: Icons.trending_down_rounded,
                                isSelected: !_isPositive,
                                gradient: AppTheme.errorGradient,
                                onTap:
                                    () => setState(() => _isPositive = false),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Save Button
                        SizedBox(
                          width: double.infinity,
                          child: modern.ModernButton(
                            text: 'إنشاء الحساب',
                            onPressed: _isLoading ? null : _saveAccount,
                            isLoading: _isLoading,
                            gradient: AppTheme.primaryGradient,
                            icon: Icons.add_rounded,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int? maxLines,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Theme.of(context).textTheme.titleMedium?.color,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines ?? 1,
          textAlign: TextAlign.right,
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyLarge?.color,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Theme.of(context).textTheme.bodyMedium?.color,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            ),
            prefixIcon: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context).cardColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildTypeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isSelected ? gradient : null,
          color: isSelected ? null : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? Colors.transparent
                    : Theme.of(context).dividerColor,
            width: 2,
          ),
          boxShadow: isSelected ? AppTheme.cardShadow : null,
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.2)
                        : Theme.of(context).dividerColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color:
                    isSelected
                        ? Colors.white
                        : Theme.of(context).textTheme.bodyMedium?.color,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color:
                    isSelected
                        ? Colors.white
                        : Theme.of(context).textTheme.titleMedium?.color,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.8)
                        : Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 10),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Build phone field with country code picker
  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رقم الهاتف',
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.textLight),
          ),
          child: Row(
            children: [
              // Country Code Picker
              Container(
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: CountryCodePicker(
                  onChanged: (country) {
                    setState(() {
                      _countryCode = country.dialCode!;
                    });
                  },
                  initialSelection: 'SA', // Saudi Arabia
                  favorite: const [
                    '+966',
                    'SA',
                    '+971',
                    'AE',
                    '+965',
                    'KW',
                    '+974',
                    'QA',
                  ],
                  showCountryOnly: false,
                  showOnlyCountryWhenClosed: false,
                  alignLeft: false,
                  textStyle: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Cairo',
                    fontSize: 14,
                  ),
                  dialogTextStyle: const TextStyle(fontFamily: 'Cairo'),
                  searchStyle: const TextStyle(fontFamily: 'Cairo'),
                  dialogBackgroundColor: Colors.white,
                  barrierColor: Colors.black54,
                  backgroundColor: Colors.transparent,
                  boxDecoration: const BoxDecoration(),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 12,
                  ),
                ),
              ),

              // Phone Number Input
              Expanded(
                child: TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.ltr,
                  style: TextStyle(
                    color: AppTheme.textPrimary,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      16,
                    ),
                  ),
                  decoration: InputDecoration(
                    hintText: '501234567',
                    hintStyle: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        14,
                      ),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      // Remove any non-digit characters for validation
                      String cleanNumber = value.replaceAll(
                        RegExp(r'[^\d]'),
                        '',
                      );
                      if (cleanNumber.length < 8) {
                        return 'رقم الهاتف قصير جداً';
                      }
                      if (cleanNumber.length > 15) {
                        return 'رقم الهاتف طويل جداً';
                      }
                    }
                    return null;
                  },
                ),
              ),

              // Phone Icon
              Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.infoGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.phone_rounded,
                  color: Colors.white,
                  size: ResponsiveHelper.getResponsiveIconSize(context, 20),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'سيتم استخدام رقم الهاتف لإرسال ملخص الحساب عبر WhatsApp أو الرسائل النصية',
          style: TextStyle(
            color: AppTheme.textSecondary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العملة',
          style: TextStyle(
            color: Theme.of(context).textTheme.titleMedium?.color,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          child: DropdownButtonFormField<Currency>(
            value: _selectedCurrency,
            decoration: InputDecoration(
              hintText: 'اختر العملة',
              hintStyle: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.currency_exchange,
                  color: Colors.white,
                  size: ResponsiveHelper.getResponsiveIconSize(context, 20),
                ),
              ),
              border: InputBorder.none,
              filled: true,
              fillColor: Theme.of(context).cardColor,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            items:
                _availableCurrencies.map((currency) {
                  return DropdownMenuItem<Currency>(
                    value: currency,
                    child: Row(
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            gradient:
                                currency.isDefault
                                    ? AppTheme.primaryGradient
                                    : LinearGradient(
                                      colors: [
                                        Theme.of(
                                          context,
                                        ).primaryColor.withValues(alpha: 0.1),
                                        Theme.of(
                                          context,
                                        ).primaryColor.withValues(alpha: 0.2),
                                      ],
                                    ),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Center(
                            child: Text(
                              currency.symbol,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color:
                                    currency.isDefault
                                        ? Colors.white
                                        : Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currency.name,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color:
                                      Theme.of(
                                        context,
                                      ).textTheme.bodyLarge?.color,
                                ),
                              ),
                              Text(
                                currency.code,
                                style: TextStyle(
                                  fontSize: 12,
                                  color:
                                      Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (currency.isDefault)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              gradient: AppTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'افتراضي',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                }).toList(),
            onChanged: (Currency? newValue) {
              setState(() {
                _selectedCurrency = newValue;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'يرجى اختيار العملة';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}
