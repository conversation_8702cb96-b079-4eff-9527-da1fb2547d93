import 'package:flutter/material.dart';
import '../models/app_settings.dart';

class LanguageService extends ChangeNotifier {
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  String _currentLanguage = 'العربية';
  Locale _currentLocale = const Locale('ar', 'SA');

  String get currentLanguage => _currentLanguage;
  Locale get currentLocale => _currentLocale;

  // Initialize language service
  void initialize(AppSettings settings) {
    _currentLanguage = settings.selectedLanguage;
    _updateLocale(_currentLanguage);
    notifyListeners();
  }

  // Update language
  void updateLanguage(String language) {
    if (language != _currentLanguage) {
      _currentLanguage = language;
      _updateLocale(language);
      notifyListeners();
    }
  }

  // Update locale based on language
  void _updateLocale(String language) {
    switch (language) {
      case 'العربية':
        _currentLocale = const Locale('ar', 'SA');
        break;
      case 'English':
        _currentLocale = const Locale('en', 'US');
        break;
      case 'Français':
        _currentLocale = const Locale('fr', 'FR');
        break;
      case 'Español':
        _currentLocale = const Locale('es', 'ES');
        break;
      default:
        _currentLocale = const Locale('ar', 'SA');
    }
  }

  // Get available languages
  List<String> getAvailableLanguages() {
    return ['العربية', 'English', 'Français', 'Español'];
  }

  // Get language code
  String getLanguageCode(String language) {
    switch (language) {
      case 'العربية':
        return 'ar';
      case 'English':
        return 'en';
      case 'Français':
        return 'fr';
      case 'Español':
        return 'es';
      default:
        return 'ar';
    }
  }

  // Check if current language is RTL
  bool get isRTL => _currentLocale.languageCode == 'ar';

  // Get text direction
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;

  // Localized strings (simplified implementation)
  final Map<String, Map<String, String>> _localizedStrings = {
    'ar': {
      'app_name': 'تطبيق المحاسبة',
      'settings': 'الإعدادات',
      'general_settings': 'الإعدادات العامة',
      'notifications': 'الإشعارات',
      'enable_notifications': 'تفعيل إشعارات التطبيق',
      'dark_mode': 'الوضع المظلم',
      'enable_dark_mode': 'تفعيل الوضع المظلم',
      'language': 'اللغة',
      'sounds': 'الأصوات',
      'enable_sounds': 'تفعيل أصوات التطبيق',
      'vibration': 'الاهتزاز',
      'enable_vibration': 'تفعيل الاهتزاز',
      'financial_settings': 'الإعدادات المالية',
      'default_currency': 'العملة الافتراضية',
      'number_format': 'تنسيق الأرقام',
      'date_format': 'تنسيق التاريخ',
      'time_format': 'تنسيق الوقت',
      'show_balance_home': 'عرض الرصيد في الصفحة الرئيسية',
      'confirm_before_delete': 'تأكيد قبل الحذف',
      'backup_settings': 'النسخ الاحتياطي',
      'auto_backup': 'النسخ الاحتياطي التلقائي',
      'backup_time': 'وقت النسخ الاحتياطي',
      'backup_frequency': 'تكرار النسخ الاحتياطي',
      'import_settings': 'استيراد إعدادات',
      'export_settings': 'تصدير إعدادات',
      'create_backup_now': 'إنشاء نسخة احتياطية الآن',
      'last_backup': 'آخر نسخة احتياطية',
      'security_privacy': 'الأمان والخصوصية',
      'password_protection': 'حماية بكلمة مرور',
      'biometric_auth': 'البصمة الحيوية',
      'change_password': 'تغيير كلمة المرور',
      'security_log': 'سجل الأمان',
      'ui_settings': 'واجهة المستخدم',
      'font_size': 'حجم الخط',
      'app_theme': 'سمة التطبيق',
      'default_transaction_type': 'نوع المعاملة الافتراضي',
      'advanced_settings': 'إعدادات متقدمة',
      'clear_all_data': 'مسح جميع البيانات',
      'reset_settings': 'إعادة تعيين الإعدادات',
      'report_bug': 'تقرير مشكلة',
      'rate_app': 'تقييم التطبيق',
      'save': 'حفظ',
      'cancel': 'إلغاء',
      'ok': 'موافق',
      'yes': 'نعم',
      'no': 'لا',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'search': 'بحث',
      'filter': 'تصفية',
      'sort': 'ترتيب',
      'export': 'تصدير',
      'import': 'استيراد',
      'backup': 'نسخ احتياطي',
      'restore': 'استرجاع',
      'accounts': 'الحسابات',
      'transactions': 'المعاملات',
      'reports': 'التقارير',
      'statistics': 'الإحصائيات',
      'credit': 'دائن',
      'debit': 'مدين',
      'balance': 'الرصيد',
      'amount': 'المبلغ',
      'date': 'التاريخ',
      'description': 'الوصف',
      'notes': 'ملاحظات',
      'total': 'الإجمالي',
      'success': 'نجح',
      'error': 'خطأ',
      'warning': 'تحذير',
      'info': 'معلومات',
    },
    'en': {
      'app_name': 'Accounting App',
      'settings': 'Settings',
      'general_settings': 'General Settings',
      'notifications': 'Notifications',
      'enable_notifications': 'Enable app notifications',
      'dark_mode': 'Dark Mode',
      'enable_dark_mode': 'Enable dark mode',
      'language': 'Language',
      'sounds': 'Sounds',
      'enable_sounds': 'Enable app sounds',
      'vibration': 'Vibration',
      'enable_vibration': 'Enable vibration',
      'financial_settings': 'Financial Settings',
      'default_currency': 'Default Currency',
      'number_format': 'Number Format',
      'date_format': 'Date Format',
      'time_format': 'Time Format',
      'show_balance_home': 'Show Balance on Home',
      'confirm_before_delete': 'Confirm Before Delete',
      'backup_settings': 'Backup Settings',
      'auto_backup': 'Auto Backup',
      'backup_time': 'Backup Time',
      'backup_frequency': 'Backup Frequency',
      'import_settings': 'Import Settings',
      'export_settings': 'Export Settings',
      'create_backup_now': 'Create Backup Now',
      'last_backup': 'Last Backup',
      'security_privacy': 'Security & Privacy',
      'password_protection': 'Password Protection',
      'biometric_auth': 'Biometric Authentication',
      'change_password': 'Change Password',
      'security_log': 'Security Log',
      'ui_settings': 'UI Settings',
      'font_size': 'Font Size',
      'app_theme': 'App Theme',
      'default_transaction_type': 'Default Transaction Type',
      'advanced_settings': 'Advanced Settings',
      'clear_all_data': 'Clear All Data',
      'reset_settings': 'Reset Settings',
      'report_bug': 'Report Bug',
      'rate_app': 'Rate App',
      'save': 'Save',
      'cancel': 'Cancel',
      'ok': 'OK',
      'yes': 'Yes',
      'no': 'No',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'export': 'Export',
      'import': 'Import',
      'backup': 'Backup',
      'restore': 'Restore',
      'accounts': 'Accounts',
      'transactions': 'Transactions',
      'reports': 'Reports',
      'statistics': 'Statistics',
      'credit': 'Credit',
      'debit': 'Debit',
      'balance': 'Balance',
      'amount': 'Amount',
      'date': 'Date',
      'description': 'Description',
      'notes': 'Notes',
      'total': 'Total',
      'success': 'Success',
      'error': 'Error',
      'warning': 'Warning',
      'info': 'Info',
    },
    'fr': {
      'app_name': 'App Comptabilité',
      'settings': 'Paramètres',
      'general_settings': 'Paramètres Généraux',
      'notifications': 'Notifications',
      'enable_notifications': 'Activer les notifications',
      'dark_mode': 'Mode Sombre',
      'enable_dark_mode': 'Activer le mode sombre',
      'language': 'Langue',
      'sounds': 'Sons',
      'enable_sounds': 'Activer les sons',
      'vibration': 'Vibration',
      'enable_vibration': 'Activer la vibration',
      'save': 'Enregistrer',
      'cancel': 'Annuler',
      'ok': 'OK',
      'yes': 'Oui',
      'no': 'Non',
    },
    'es': {
      'app_name': 'App Contabilidad',
      'settings': 'Configuración',
      'general_settings': 'Configuración General',
      'notifications': 'Notificaciones',
      'enable_notifications': 'Habilitar notificaciones',
      'dark_mode': 'Modo Oscuro',
      'enable_dark_mode': 'Habilitar modo oscuro',
      'language': 'Idioma',
      'sounds': 'Sonidos',
      'enable_sounds': 'Habilitar sonidos',
      'vibration': 'Vibración',
      'enable_vibration': 'Habilitar vibración',
      'save': 'Guardar',
      'cancel': 'Cancelar',
      'ok': 'OK',
      'yes': 'Sí',
      'no': 'No',
    },
  };

  // Get localized string
  String getString(String key) {
    final languageCode = getLanguageCode(_currentLanguage);
    return _localizedStrings[languageCode]?[key] ?? key;
  }

  // Get localized string with parameters
  String getStringWithParams(String key, Map<String, String> params) {
    String text = getString(key);
    params.forEach((param, value) {
      text = text.replaceAll('{$param}', value);
    });
    return text;
  }

  // Format number based on locale
  String formatNumber(double number) {
    // Simplified number formatting
    if (isRTL) {
      return number.toStringAsFixed(2).replaceAll('.', '٫');
    } else {
      return number.toStringAsFixed(2);
    }
  }

  // Format currency based on locale
  String formatCurrency(double amount, String currency) {
    final formattedNumber = formatNumber(amount);
    
    if (isRTL) {
      switch (currency) {
        case 'ريال سعودي':
          return '$formattedNumber ر.س';
        case 'دولار أمريكي':
          return '$formattedNumber \$';
        case 'يورو':
          return '$formattedNumber €';
        default:
          return '$formattedNumber ر.س';
      }
    } else {
      switch (currency) {
        case 'ريال سعودي':
          return 'SAR $formattedNumber';
        case 'دولار أمريكي':
          return '\$$formattedNumber';
        case 'يورو':
          return '€$formattedNumber';
        default:
          return '\$$formattedNumber';
      }
    }
  }

  // Format date based on locale
  String formatDate(DateTime date) {
    if (isRTL) {
      return '${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  // Get month names
  List<String> getMonthNames() {
    if (isRTL) {
      return [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
    } else {
      return [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
    }
  }

  // Get day names
  List<String> getDayNames() {
    if (isRTL) {
      return ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    } else {
      return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    }
  }
}
