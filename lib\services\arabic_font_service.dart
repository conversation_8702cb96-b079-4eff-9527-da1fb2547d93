import 'package:flutter/services.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';

class ArabicFontService {
  static final ArabicFontService _instance = ArabicFontService._internal();
  factory ArabicFontService() => _instance;
  ArabicFontService._internal();

  pw.Font? _arabicFont;
  pw.Font? _arabicBoldFont;
  bool _fontsLoaded = false;

  // Load Arabic fonts for PDF
  Future<void> loadArabicFonts() async {
    if (_fontsLoaded) return;

    try {
      // Try to load embedded fonts
      _arabicFont = await _loadEmbeddedFont(
        'assets/fonts/NotoSansArabic-Bold.ttf',
      );
      _arabicBoldFont = await _loadEmbeddedFont(
        'assets/fonts/NotoSansArabic-Bold.ttf',
      );

      _fontsLoaded = true;
    } catch (e) {
      // Use system fonts as fallback
      _fontsLoaded = true;
    }
  }

  // Load embedded font from assets
  Future<pw.Font?> _loadEmbeddedFont(String assetPath) async {
    try {
      final fontData = await rootBundle.load(assetPath);
      return pw.Font.ttf(fontData);
    } catch (e) {
      return null;
    }
  }

  // Get Arabic text style for PDF
  pw.TextStyle getArabicTextStyle({
    double fontSize = 12,
    bool bold = false,
    PdfColor? color,
  }) {
    return pw.TextStyle(
      font: _arabicFont, // Use available font or null for system default
      fontSize: fontSize,
      color: color ?? PdfColors.black,
      fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
    );
  }

  // Get Arabic header style
  pw.TextStyle getArabicHeaderStyle({double fontSize = 18, PdfColor? color}) {
    return pw.TextStyle(
      font: _arabicFont, // Use available font or null for system default
      fontSize: fontSize,
      color: color ?? PdfColors.black,
      fontWeight: pw.FontWeight.bold,
    );
  }

  // Check if fonts are loaded
  bool get fontsLoaded => _fontsLoaded;

  // Get regular Arabic font
  pw.Font? get arabicFont => _arabicFont;

  // Get bold Arabic font
  pw.Font? get arabicBoldFont => _arabicBoldFont;
}
