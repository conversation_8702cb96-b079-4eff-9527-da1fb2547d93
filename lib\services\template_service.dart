import 'package:shared_preferences/shared_preferences.dart';
import '../models/report_template.dart';

class TemplateService {
  static const String _selectedTemplateKey = 'selected_report_template';
  static TemplateService? _instance;
  
  TemplateService._();
  
  static TemplateService get instance {
    _instance ??= TemplateService._();
    return _instance!;
  }

  // Get currently selected template
  Future<ReportTemplate> getSelectedTemplate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templateId = prefs.getString(_selectedTemplateKey);
      
      if (templateId != null) {
        final template = ReportTemplate.getTemplateById(templateId);
        if (template != null) {
          return template;
        }
      }
      
      // Return default template if none selected
      return ReportTemplate.modern;
    } catch (e) {
      return ReportTemplate.modern;
    }
  }

  // Set selected template
  Future<bool> setSelectedTemplate(ReportTemplate template) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_selectedTemplateKey, template.id);
    } catch (e) {
      return false;
    }
  }

  // Get all available templates
  List<ReportTemplate> getAllTemplates() {
    return ReportTemplate.getAllTemplates();
  }

  // Reset to default template
  Future<bool> resetToDefault() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_selectedTemplateKey);
    } catch (e) {
      return false;
    }
  }

  // Check if template is currently selected
  Future<bool> isTemplateSelected(ReportTemplate template) async {
    final currentTemplate = await getSelectedTemplate();
    return currentTemplate.id == template.id;
  }
}
