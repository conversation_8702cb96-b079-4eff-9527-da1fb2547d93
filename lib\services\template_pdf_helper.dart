import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import '../models/report_template.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import 'arabic_font_service.dart';
import 'barcode_service.dart';

class TemplatePdfHelper {
  static final ArabicFontService _fontService = ArabicFontService();

  // Expose font service for external use
  static ArabicFontService get fontService => _fontService;

  // Initialize fonts
  static Future<void> initializeFonts() async {
    await _fontService.loadArabicFonts();
  }

  // Helper function to get contrasting text color
  static PdfColor _getContrastingTextColor(PdfColor backgroundColor) {
    // حساب سطوع اللون
    final brightness =
        (backgroundColor.red * 299 +
            backgroundColor.green * 587 +
            backgroundColor.blue * 114) /
        1000;

    // إذا كان اللون فاتح، استخدم نص داكن، وإذا كان داكن، استخدم نص فاتح
    return brightness > 0.5 ? PdfColors.black : PdfColors.white;
  }

  // Create enhanced document header with barcode
  static pw.Widget createTemplateHeader({
    required ReportTemplate template,
    required String title,
    required String subtitle,
    String? date,
    Account? account,
    Map<String, dynamic>? reportData,
  }) {
    // تحديد لون النص المناسب للخلفية
    final textColor = _getContrastingTextColor(template.pdfPrimaryColor);
    final subtitleColor =
        textColor == PdfColors.white
            ? PdfColors.white.shade(0.9)
            : PdfColors.black.shade(0.7);

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(24),
      decoration: pw.BoxDecoration(
        gradient:
            template.hasGradient
                ? pw.LinearGradient(
                  colors: template.pdfGradientColors,
                  begin: pw.Alignment.topLeft,
                  end: pw.Alignment.bottomRight,
                )
                : null,
        color: template.hasGradient ? null : template.pdfPrimaryColor,
        borderRadius: pw.BorderRadius.circular(16),
        boxShadow: [
          pw.BoxShadow(
            color: template.pdfPrimaryColor.shade(0.3),
            offset: const PdfPoint(0, 6),
            blurRadius: 12,
          ),
        ],
      ),
      child: pw.Row(
        children: [
          // معلومات الرأس
          pw.Expanded(
            flex: 3,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان الرئيسي مع خلفية مزخرفة
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: pw.BoxDecoration(
                    color:
                        textColor == PdfColors.white
                            ? PdfColors.white.shade(0.15)
                            : PdfColors.black.shade(0.1),
                    borderRadius: pw.BorderRadius.circular(12),
                    border: pw.Border.all(
                      color:
                          textColor == PdfColors.white
                              ? PdfColors.white.shade(0.3)
                              : PdfColors.black.shade(0.2),
                      width: 1,
                    ),
                  ),
                  child: pw.Text(
                    title,
                    style: _fontService.getArabicHeaderStyle(
                      fontSize: 22,
                      color: textColor,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),

                pw.SizedBox(height: 12),

                // العنوان الفرعي
                pw.Text(
                  subtitle,
                  style: _fontService.getArabicTextStyle(
                    fontSize: 16,
                    color: subtitleColor,
                    bold: true,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),

                if (date != null) ...[
                  pw.SizedBox(height: 8),
                  // التاريخ مع أيقونة
                  pw.Row(
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        decoration: pw.BoxDecoration(
                          color:
                              textColor == PdfColors.white
                                  ? PdfColors.white.shade(0.2)
                                  : PdfColors.black.shade(0.1),
                          borderRadius: pw.BorderRadius.circular(6),
                        ),
                        child: pw.Icon(
                          pw.IconData(0xe192), // calendar icon
                          color: textColor,
                          size: 14,
                        ),
                      ),
                      pw.SizedBox(width: 8),
                      pw.Text(
                        date,
                        style: _fontService.getArabicTextStyle(
                          fontSize: 14,
                          color: subtitleColor,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          pw.SizedBox(width: 20),

          // الباركود والأيقونة
          pw.Column(
            children: [
              // أيقونة مزخرفة
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color:
                      textColor == PdfColors.white
                          ? PdfColors.white.shade(0.2)
                          : PdfColors.black.shade(0.1),
                  borderRadius: pw.BorderRadius.circular(16),
                  border: pw.Border.all(
                    color:
                        textColor == PdfColors.white
                            ? PdfColors.white.shade(0.4)
                            : PdfColors.black.shade(0.2),
                    width: 2,
                  ),
                ),
                child: pw.Icon(
                  pw.IconData(0xe88a), // receipt icon
                  color: textColor,
                  size: 32,
                ),
              ),

              pw.SizedBox(height: 12),

              // الباركود
              if (account != null)
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.white,
                    borderRadius: pw.BorderRadius.circular(8),
                    boxShadow: [
                      pw.BoxShadow(
                        color: PdfColors.black.shade(0.15),
                        blurRadius: 6,
                        offset: const PdfPoint(0, 3),
                      ),
                    ],
                  ),
                  child: BarcodeService.createAccountBarcode(account),
                )
              else if (reportData != null)
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.white,
                    borderRadius: pw.BorderRadius.circular(8),
                    boxShadow: [
                      pw.BoxShadow(
                        color: PdfColors.black.shade(0.15),
                        blurRadius: 6,
                        offset: const PdfPoint(0, 3),
                      ),
                    ],
                  ),
                  child: BarcodeService.createReportBarcode(title, reportData),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Create summary section with template styling
  static pw.Widget createTemplateSummary({
    required ReportTemplate template,
    required String title,
    required List<String> summaryItems,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: template.pdfBackgroundColor,
        border: pw.Border.all(color: template.pdfPrimaryColor, width: 1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Section title
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: pw.BoxDecoration(
              color: template.pdfAccentColor,
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Text(
              title,
              style: _fontService.getArabicTextStyle(
                fontSize: 16,
                bold: true,
                color: template.pdfPrimaryColor,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          pw.SizedBox(height: 12),

          // Summary items
          ...summaryItems.map(
            (item) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 6),
              child: pw.Row(
                children: [
                  pw.Container(
                    width: 6,
                    height: 6,
                    decoration: pw.BoxDecoration(
                      color: template.pdfAccentColor,
                      borderRadius: pw.BorderRadius.circular(3),
                    ),
                  ),
                  pw.SizedBox(width: 8),
                  pw.Expanded(
                    child: pw.Text(
                      item,
                      style: _fontService.getArabicTextStyle(
                        fontSize: 12,
                        color: template.pdfTextColor,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Create enhanced table with template styling and optional barcodes
  static pw.Widget createTemplateTable({
    required ReportTemplate template,
    required List<String> headers,
    required List<List<String>> data,
    List<PdfColor?>? rowColors,
    List<bool>? boldColumns,
    List<Transaction>? transactions,
    List<Account>? accounts,
    bool showBarcodes = false,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(8),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Table(
        border: pw.TableBorder.all(color: template.pdfPrimaryColor, width: 0.5),
        children: [
          // Header row
          pw.TableRow(
            decoration: pw.BoxDecoration(
              gradient:
                  template.hasGradient
                      ? pw.LinearGradient(
                        colors: template.pdfGradientColors,
                        begin: pw.Alignment.topLeft,
                        end: pw.Alignment.bottomRight,
                      )
                      : null,
              color: template.hasGradient ? null : template.pdfPrimaryColor,
            ),
            children:
                headers
                    .map(
                      (header) => pw.Container(
                        padding: const pw.EdgeInsets.all(12),
                        child: pw.Text(
                          header,
                          style: _fontService.getArabicTextStyle(
                            fontSize: 12,
                            bold: true,
                            color: template.pdfHeaderTextColor,
                          ),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ),
                    )
                    .toList(),
          ),

          // Data rows with enhanced styling
          ...data.asMap().entries.map((entry) {
            final index = entry.key;
            final row = entry.value;
            final isEven = index % 2 == 0;
            final transaction =
                transactions != null && index < transactions.length
                    ? transactions[index]
                    : null;
            final account =
                accounts != null && index < accounts.length
                    ? accounts[index]
                    : null;

            return pw.TableRow(
              decoration: pw.BoxDecoration(
                color:
                    isEven
                        ? template.pdfBackgroundColor
                        : template.pdfPrimaryColor.shade(0.05),
                border: pw.Border(
                  bottom: pw.BorderSide(
                    color: template.pdfPrimaryColor.shade(0.2),
                    width: 0.5,
                  ),
                ),
              ),
              children: [
                // إضافة عمود الباركود إذا كان مطلوباً
                if (showBarcodes && (transaction != null || account != null))
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Center(
                      child:
                          transaction != null && account != null
                              ? BarcodeService.createTransactionBarcode(
                                transaction,
                                account,
                              )
                              : account != null
                              ? BarcodeService.createAccountBarcode(account)
                              : pw.SizedBox(width: 40, height: 40),
                    ),
                  ),

                // باقي الأعمدة
                ...row.asMap().entries.map((cellEntry) {
                  final colIndex = cellEntry.key;
                  final cell = cellEntry.value;
                  final isBold =
                      boldColumns != null &&
                      colIndex < boldColumns.length &&
                      boldColumns[colIndex];

                  return pw.Container(
                    padding: const pw.EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    child: pw.Text(
                      cell,
                      style: _fontService.getArabicTextStyle(
                        fontSize: 11,
                        bold: isBold,
                        color: template.pdfTextColor,
                      ),
                      textAlign: pw.TextAlign.center,
                      textDirection: pw.TextDirection.rtl,
                    ),
                  );
                }),
              ],
            );
          }),
        ],
      ),
    );
  }

  // Create enhanced footer with template styling
  static pw.Widget createTemplateFooter({
    required ReportTemplate template,
    String? footerText,
    String? companyName,
    String? contactInfo,
  }) {
    final textColor = _getContrastingTextColor(template.pdfPrimaryColor);
    final subtitleColor =
        textColor == PdfColors.white
            ? PdfColors.white.shade(0.8)
            : PdfColors.black.shade(0.6);

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient:
            template.hasGradient
                ? pw.LinearGradient(
                  colors: template.pdfGradientColors.reversed.toList(),
                  begin: pw.Alignment.topLeft,
                  end: pw.Alignment.bottomRight,
                )
                : null,
        color: template.hasGradient ? null : template.pdfPrimaryColor,
        borderRadius: pw.BorderRadius.circular(12),
        boxShadow: [
          pw.BoxShadow(
            color: template.pdfPrimaryColor.shade(0.3),
            offset: const PdfPoint(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: pw.Column(
        children: [
          // خط فاصل مزخرف
          pw.Container(
            height: 2,
            width: 100,
            decoration: pw.BoxDecoration(
              color:
                  textColor == PdfColors.white
                      ? PdfColors.white.shade(0.5)
                      : PdfColors.black.shade(0.3),
              borderRadius: pw.BorderRadius.circular(1),
            ),
          ),

          pw.SizedBox(height: 12),

          // النص الرئيسي
          pw.Text(
            footerText ??
                'تم إنشاء هذا التقرير بواسطة نظام إدارة الحسابات العصري',
            style: _fontService.getArabicTextStyle(
              fontSize: 12,
              color: textColor,
              bold: true,
            ),
            textAlign: pw.TextAlign.center,
            textDirection: pw.TextDirection.rtl,
          ),

          if (companyName != null || contactInfo != null) ...[
            pw.SizedBox(height: 8),

            // معلومات الشركة
            if (companyName != null)
              pw.Text(
                companyName,
                style: _fontService.getArabicTextStyle(
                  fontSize: 10,
                  color: subtitleColor,
                ),
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.rtl,
              ),

            if (contactInfo != null) ...[
              pw.SizedBox(height: 4),
              pw.Text(
                contactInfo,
                style: _fontService.getArabicTextStyle(
                  fontSize: 9,
                  color: subtitleColor,
                ),
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ],

          pw.SizedBox(height: 8),

          // تاريخ الإنشاء
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Icon(
                pw.IconData(0xe192), // calendar icon
                color: subtitleColor,
                size: 10,
              ),
              pw.SizedBox(width: 4),
              pw.Text(
                'تاريخ الإنشاء: ${DateTime.now().toString().split(' ')[0]}',
                style: _fontService.getArabicTextStyle(
                  fontSize: 8,
                  color: subtitleColor,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Create empty state with template styling
  static pw.Widget createTemplateEmptyState({
    required ReportTemplate template,
    required String message,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(40),
      decoration: pw.BoxDecoration(
        color: template.pdfBackgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: template.pdfPrimaryColor, width: 1),
      ),
      child: pw.Center(
        child: pw.Text(
          message,
          style: _fontService.getArabicTextStyle(
            fontSize: 16,
            color: template.pdfTextColor,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ),
    );
  }
}
