import 'package:pdf/pdf.dart';

enum ReportTemplateType { modern, elegant, corporate, creative, minimal }

class ReportTemplate {
  final String id;
  final String name;
  final String description;
  final ReportTemplateType type;
  final int primaryColor;
  final int secondaryColor;
  final int accentColor;
  final int backgroundColor;
  final int textColor;
  final int headerTextColor;
  final String previewImage;
  final bool hasGradient;
  final List<int> gradientColors;

  const ReportTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.backgroundColor,
    required this.textColor,
    required this.headerTextColor,
    required this.previewImage,
    this.hasGradient = false,
    this.gradientColors = const [],
  });

  // Modern Template - Blue & Teal
  static const ReportTemplate modern = ReportTemplate(
    id: 'modern',
    name: 'عصري',
    description: 'تصميم عصري بألوان زرقاء وتركوازية',
    type: ReportTemplateType.modern,
    primaryColor: 0xFF2196F3,
    secondaryColor: 0xFF00BCD4,
    accentColor: 0xFF03DAC6,
    backgroundColor: 0xFFF8F9FA,
    textColor: 0xFF212529,
    headerTextColor: 0xFFFFFFFF,
    previewImage: 'assets/images/template_modern.png',
    hasGradient: true,
    gradientColors: [0xFF2196F3, 0xFF00BCD4],
  );

  // Elegant Template - Purple & Gold
  static const ReportTemplate elegant = ReportTemplate(
    id: 'elegant',
    name: 'أنيق',
    description: 'تصميم أنيق بألوان بنفسجية وذهبية',
    type: ReportTemplateType.elegant,
    primaryColor: 0xFF6A1B9A,
    secondaryColor: 0xFF9C27B0,
    accentColor: 0xFFFFD700,
    backgroundColor: 0xFFFAF7FF,
    textColor: 0xFF2E2E2E,
    headerTextColor: 0xFFFFFFFF,
    previewImage: 'assets/images/template_elegant.png',
    hasGradient: true,
    gradientColors: [0xFF6A1B9A, 0xFF9C27B0],
  );

  // Corporate Template - Navy & Gray
  static const ReportTemplate corporate = ReportTemplate(
    id: 'corporate',
    name: 'مؤسسي',
    description: 'تصميم مؤسسي بألوان كحلية ورمادية',
    type: ReportTemplateType.corporate,
    primaryColor: 0xFF1A237E,
    secondaryColor: 0xFF3F51B5,
    accentColor: 0xFF607D8B,
    backgroundColor: 0xFFFFFFFF,
    textColor: 0xFF263238,
    headerTextColor: 0xFFFFFFFF,
    previewImage: 'assets/images/template_corporate.png',
    hasGradient: false,
  );

  // Creative Template - Orange & Pink
  static const ReportTemplate creative = ReportTemplate(
    id: 'creative',
    name: 'إبداعي',
    description: 'تصميم إبداعي بألوان برتقالية ووردية',
    type: ReportTemplateType.creative,
    primaryColor: 0xFFFF5722,
    secondaryColor: 0xFFE91E63,
    accentColor: 0xFFFF9800,
    backgroundColor: 0xFFFFF8F5,
    textColor: 0xFF3E2723,
    headerTextColor: 0xFFFFFFFF,
    previewImage: 'assets/images/template_creative.png',
    hasGradient: true,
    gradientColors: [0xFFFF5722, 0xFFE91E63],
  );

  // Minimal Template - Green & White
  static const ReportTemplate minimal = ReportTemplate(
    id: 'minimal',
    name: 'بسيط',
    description: 'تصميم بسيط بألوان خضراء وبيضاء',
    type: ReportTemplateType.minimal,
    primaryColor: 0xFF4CAF50,
    secondaryColor: 0xFF8BC34A,
    accentColor: 0xFF009688,
    backgroundColor: 0xFFFFFFFF,
    textColor: 0xFF1B5E20,
    headerTextColor: 0xFFFFFFFF,
    previewImage: 'assets/images/template_minimal.png',
    hasGradient: false,
  );

  // Get all available templates
  static List<ReportTemplate> getAllTemplates() {
    return [modern, elegant, corporate, creative, minimal];
  }

  // Get template by ID
  static ReportTemplate? getTemplateById(String id) {
    try {
      return getAllTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get template by type
  static ReportTemplate getTemplateByType(ReportTemplateType type) {
    return getAllTemplates().firstWhere(
      (template) => template.type == type,
      orElse: () => modern,
    );
  }

  // Convert to Map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString(),
    };
  }

  // Create from Map
  factory ReportTemplate.fromMap(Map<String, dynamic> map) {
    final typeString = map['type'] as String;
    final type = ReportTemplateType.values.firstWhere(
      (e) => e.toString() == typeString,
      orElse: () => ReportTemplateType.modern,
    );
    return getTemplateByType(type);
  }

  // Helper methods to convert int colors to PdfColor
  PdfColor get pdfPrimaryColor => PdfColor.fromInt(primaryColor);
  PdfColor get pdfSecondaryColor => PdfColor.fromInt(secondaryColor);
  PdfColor get pdfAccentColor => PdfColor.fromInt(accentColor);
  PdfColor get pdfBackgroundColor => PdfColor.fromInt(backgroundColor);
  PdfColor get pdfTextColor => PdfColor.fromInt(textColor);
  PdfColor get pdfHeaderTextColor => PdfColor.fromInt(headerTextColor);

  List<PdfColor> get pdfGradientColors =>
      gradientColors.map((color) => PdfColor.fromInt(color)).toList();
}
