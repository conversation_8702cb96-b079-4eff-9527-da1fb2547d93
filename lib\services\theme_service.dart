import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/app_settings.dart';
import '../theme/app_theme.dart';

class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  ThemeMode _themeMode = ThemeMode.light;
  String _selectedTheme = 'فاتح';
  double _fontSize = 16.0;

  ThemeMode get themeMode => _themeMode;
  String get selectedTheme => _selectedTheme;
  double get fontSize => _fontSize;

  // Initialize theme service
  void initialize(AppSettings settings) {
    _selectedTheme = settings.appTheme;
    _fontSize = settings.fontSize;
    _updateThemeMode(settings.darkModeEnabled);

    // Update system UI overlay style
    _updateSystemUIOverlay();

    notifyListeners();
  }

  // Update theme settings
  void updateTheme({String? theme, bool? darkMode, double? fontSize}) {
    bool hasChanges = false;

    if (theme != null && theme != _selectedTheme) {
      _selectedTheme = theme;
      hasChanges = true;
    }

    if (fontSize != null && fontSize != _fontSize) {
      _fontSize = fontSize;
      hasChanges = true;
    }

    if (darkMode != null) {
      _updateThemeMode(darkMode);
      hasChanges = true;
    }

    if (hasChanges) {
      _updateSystemUIOverlay();
      notifyListeners();
    }
  }

  // Update theme mode
  void _updateThemeMode(bool darkMode) {
    _themeMode = darkMode ? ThemeMode.dark : ThemeMode.light;
  }

  // Update system UI overlay style
  void _updateSystemUIOverlay() {
    final bool isDark = _themeMode == ThemeMode.dark;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark ? Colors.black : Colors.white,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  // Get light theme
  ThemeData get lightTheme {
    // Use the modern theme as base and customize with user preferences
    final modernTheme = AppTheme.lightTheme;
    final customColorScheme = _getColorScheme(false);

    return modernTheme.copyWith(
      colorScheme: customColorScheme,
      textTheme: _getTextTheme(false),
      appBarTheme: modernTheme.appBarTheme.copyWith(
        titleTextStyle: modernTheme.appBarTheme.titleTextStyle?.copyWith(
          fontSize: _fontSize + 4,
        ),
      ),
    );
  }

  // Get dark theme
  ThemeData get darkTheme {
    // Create a dark version of the modern theme
    final lightTheme = AppTheme.lightTheme;
    final customColorScheme = _getColorScheme(true);

    return lightTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: customColorScheme,
      textTheme: _getTextTheme(true),
      appBarTheme: lightTheme.appBarTheme.copyWith(
        backgroundColor: customColorScheme.surface,
        foregroundColor: customColorScheme.onSurface,
        titleTextStyle: lightTheme.appBarTheme.titleTextStyle?.copyWith(
          fontSize: _fontSize + 4,
          color: customColorScheme.onSurface,
        ),
      ),
      cardTheme: lightTheme.cardTheme.copyWith(
        color: customColorScheme.surface,
      ),
    );
  }

  // Get color scheme based on selected theme
  ColorScheme _getColorScheme(bool isDark) {
    Color primaryColor;

    switch (_selectedTheme) {
      case 'أزرق':
        primaryColor = const Color(0xFF2196F3); // Modern Blue
        break;
      case 'أخضر':
        primaryColor = const Color(0xFF4CAF50); // Modern Green
        break;
      case 'بنفسجي':
        primaryColor = const Color(0xFF9C27B0); // Modern Purple
        break;
      case 'برتقالي':
        primaryColor = const Color(0xFFFF9800); // Modern Orange
        break;
      case 'وردي':
        primaryColor = const Color(0xFFE91E63); // Modern Pink
        break;
      default:
        primaryColor = AppTheme.primaryColor; // Modern Purple from AppTheme
    }

    if (isDark) {
      return ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      );
    } else {
      return ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      );
    }
  }

  // Get text theme with custom font size
  TextTheme _getTextTheme(bool isDark) {
    final baseTheme =
        isDark ? ThemeData.dark().textTheme : ThemeData.light().textTheme;

    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(fontSize: _fontSize + 16),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: _fontSize + 12,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(fontSize: _fontSize + 8),
      headlineLarge: baseTheme.headlineLarge?.copyWith(fontSize: _fontSize + 8),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: _fontSize + 6,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(fontSize: _fontSize + 4),
      titleLarge: baseTheme.titleLarge?.copyWith(fontSize: _fontSize + 4),
      titleMedium: baseTheme.titleMedium?.copyWith(fontSize: _fontSize + 2),
      titleSmall: baseTheme.titleSmall?.copyWith(fontSize: _fontSize),
      bodyLarge: baseTheme.bodyLarge?.copyWith(fontSize: _fontSize + 2),
      bodyMedium: baseTheme.bodyMedium?.copyWith(fontSize: _fontSize),
      bodySmall: baseTheme.bodySmall?.copyWith(fontSize: _fontSize - 2),
      labelLarge: baseTheme.labelLarge?.copyWith(fontSize: _fontSize),
      labelMedium: baseTheme.labelMedium?.copyWith(fontSize: _fontSize - 2),
      labelSmall: baseTheme.labelSmall?.copyWith(fontSize: _fontSize - 4),
    );
  }

  // Get app bar theme
  AppBarTheme _getAppBarTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return AppBarTheme(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontSize: _fontSize + 4,
        fontWeight: FontWeight.bold,
        color: colorScheme.onPrimary,
      ),
    );
  }

  // Get card theme
  CardTheme _getCardTheme(bool isDark) {
    return CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    );
  }

  // Get elevated button theme
  ElevatedButtonThemeData _getElevatedButtonTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        textStyle: TextStyle(fontSize: _fontSize),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  // Get input decoration theme
  InputDecorationTheme _getInputDecorationTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return InputDecorationTheme(
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      labelStyle: TextStyle(fontSize: _fontSize),
      hintStyle: TextStyle(fontSize: _fontSize),
    );
  }

  // Get drawer theme
  DrawerThemeData _getDrawerTheme(bool isDark) {
    return const DrawerThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
    );
  }

  // Get bottom navigation bar theme
  BottomNavigationBarThemeData _getBottomNavigationBarTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return BottomNavigationBarThemeData(
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
      selectedLabelStyle: TextStyle(fontSize: _fontSize - 2),
      unselectedLabelStyle: TextStyle(fontSize: _fontSize - 2),
    );
  }

  // Get floating action button theme
  FloatingActionButtonThemeData _getFloatingActionButtonTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
  }

  // Get switch theme
  SwitchThemeData _getSwitchTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return null;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary.withValues(alpha: 0.5);
        }
        return null;
      }),
    );
  }

  // Get slider theme
  SliderThemeData _getSliderTheme(bool isDark) {
    final colorScheme = _getColorScheme(isDark);

    return SliderThemeData(
      activeTrackColor: colorScheme.primary,
      thumbColor: colorScheme.primary,
      overlayColor: colorScheme.primary.withValues(alpha: 0.2),
    );
  }

  // Toggle theme mode
  void toggleTheme() {
    _themeMode =
        _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    _updateSystemUIOverlay();
    notifyListeners();
  }

  // Get theme display name
  String getThemeDisplayName(String theme) {
    switch (theme) {
      case 'فاتح':
        return 'فاتح';
      case 'مظلم':
        return 'مظلم';
      case 'تلقائي':
        return 'تلقائي';
      case 'أزرق':
        return 'أزرق';
      case 'أخضر':
        return 'أخضر';
      case 'بنفسجي':
        return 'بنفسجي';
      default:
        return 'فاتح';
    }
  }

  // Check if current theme is dark
  bool get isDarkMode => _themeMode == ThemeMode.dark;

  // Get current primary color
  Color get primaryColor {
    switch (_selectedTheme) {
      case 'أزرق':
        return Colors.blue;
      case 'أخضر':
        return Colors.green;
      case 'بنفسجي':
        return Colors.purple;
      default:
        return const Color(0xFF4A5FC7);
    }
  }
}
