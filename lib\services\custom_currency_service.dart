import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/currency.dart';

class CustomCurrencyService {
  static const String _tableName = 'currencies';

  // Get database instance
  Future<Database> get _database async {
    return await DatabaseHelper().database;
  }

  // Initialize currencies table
  Future<void> initializeCurrenciesTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $_tableName (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        isDefault INTEGER NOT NULL DEFAULT 0,
        createdAt INTEGER NOT NULL
      )
    ''');
  }

  // Add default currencies if none exist
  Future<void> addDefaultCurrenciesIfEmpty() async {
    final currencies = await getAllCurrencies();
    if (currencies.isEmpty) {
      final defaultCurrencies = Currency.getDefaultCurrencies();
      for (final currency in defaultCurrencies) {
        await addCurrency(currency);
      }
    }
  }

  // Add a new currency
  Future<bool> addCurrency(Currency currency) async {
    try {
      final db = await _database;

      // Check if code already exists
      final existing = await getCurrencyByCode(currency.code);
      if (existing != null) {
        return false; // Currency with this code already exists
      }

      await db.insert(_tableName, currency.toMap());
      return true;
    } catch (e) {
      print('Error adding currency: $e');
      return false;
    }
  }

  // Get all currencies
  Future<List<Currency>> getAllCurrencies() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        orderBy: 'isDefault DESC, name ASC',
      );

      return List.generate(maps.length, (i) {
        return Currency.fromMap(maps[i]);
      });
    } catch (e) {
      print('Error getting currencies: $e');
      return [];
    }
  }

  // Get currency by ID
  Future<Currency?> getCurrencyById(String id) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Error getting currency by ID: $e');
      return null;
    }
  }

  // Get currency by code
  Future<Currency?> getCurrencyByCode(String code) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'code = ?',
        whereArgs: [code],
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Error getting currency by code: $e');
      return null;
    }
  }

  // Get default currency
  Future<Currency?> getDefaultCurrency() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'isDefault = ?',
        whereArgs: [1],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }

      // If no default currency, return the first available currency
      final allCurrencies = await getAllCurrencies();
      if (allCurrencies.isNotEmpty) {
        return allCurrencies.first;
      }

      return null;
    } catch (e) {
      print('Error getting default currency: $e');
      return null;
    }
  }

  // Set default currency
  Future<bool> setDefaultCurrency(String currencyId) async {
    try {
      final db = await _database;

      // First, remove default flag from all currencies
      await db.update(
        _tableName,
        {'isDefault': 0},
        where: 'isDefault = ?',
        whereArgs: [1],
      );

      // Then set the new default currency
      final result = await db.update(
        _tableName,
        {'isDefault': 1},
        where: 'id = ?',
        whereArgs: [currencyId],
      );

      return result > 0;
    } catch (e) {
      print('Error setting default currency: $e');
      return false;
    }
  }

  // Update currency
  Future<bool> updateCurrency(Currency currency) async {
    try {
      final db = await _database;

      // Check if another currency with the same code exists (excluding current currency)
      final existing = await db.query(
        _tableName,
        where: 'code = ? AND id != ?',
        whereArgs: [currency.code, currency.id],
      );

      if (existing.isNotEmpty) {
        return false; // Another currency with this code already exists
      }

      final result = await db.update(
        _tableName,
        currency.toMap(),
        where: 'id = ?',
        whereArgs: [currency.id],
      );

      return result > 0;
    } catch (e) {
      print('Error updating currency: $e');
      return false;
    }
  }

  // Delete currency
  Future<bool> deleteCurrency(String currencyId) async {
    try {
      final db = await _database;

      // Check if this currency is being used by any accounts
      final accountsUsingCurrency = await db.query(
        'accounts',
        where: 'currencyId = ?',
        whereArgs: [currencyId],
      );

      if (accountsUsingCurrency.isNotEmpty) {
        return false; // Cannot delete currency that is being used
      }

      final result = await db.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [currencyId],
      );

      return result > 0;
    } catch (e) {
      print('Error deleting currency: $e');
      return false;
    }
  }

  // Get currencies used by accounts
  Future<List<Currency>> getCurrenciesUsedByAccounts() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT DISTINCT c.* FROM $_tableName c
        INNER JOIN accounts a ON c.id = a.currencyId
        ORDER BY c.name ASC
      ''');

      return List.generate(maps.length, (i) {
        return Currency.fromMap(maps[i]);
      });
    } catch (e) {
      print('Error getting currencies used by accounts: $e');
      return [];
    }
  }

  // Check if currency code is available
  Future<bool> isCurrencyCodeAvailable(String code, {String? excludeId}) async {
    try {
      final db = await _database;
      String whereClause = 'code = ?';
      List<dynamic> whereArgs = [code];

      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: whereClause,
        whereArgs: whereArgs,
      );

      return maps.isEmpty;
    } catch (e) {
      print('Error checking currency code availability: $e');
      return false;
    }
  }

  // Get currency count
  Future<int> getCurrencyCount() async {
    try {
      final db = await _database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $_tableName',
      );
      return result.first['count'] as int;
    } catch (e) {
      print('Error getting currency count: $e');
      return 0;
    }
  }

  // Search currencies
  Future<List<Currency>> searchCurrencies(String query) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'name LIKE ? OR code LIKE ? OR symbol LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'isDefault DESC, name ASC',
      );

      return List.generate(maps.length, (i) {
        return Currency.fromMap(maps[i]);
      });
    } catch (e) {
      print('Error searching currencies: $e');
      return [];
    }
  }
}
