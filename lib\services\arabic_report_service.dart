import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';
import 'template_pdf_helper.dart';
import 'template_service.dart';
import 'enhanced_template_helper.dart';

class ArabicReportService {
  static final ArabicReportService _instance = ArabicReportService._internal();
  factory ArabicReportService() => _instance;
  ArabicReportService._internal();

  final DatabaseHelper _storage = DatabaseHelper();
  final TemplateService _templateService = TemplateService.instance;

  // Generate PDF Report for All Accounts with Arabic support
  Future<File> generateAccountsPDFReport() async {
    // Initialize fonts
    await TemplatePdfHelper.initializeFonts();
    await EnhancedTemplateHelper.initializeFonts();

    final pdf = pw.Document();
    final accounts = await _storage.getAllAccounts();
    final template = await _templateService.getSelectedTemplate();
    final now = DateTime.now();
    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // Stunning enhanced header with barcode
            EnhancedTemplateHelper.createStunningHeader(
              template: template,
              title: 'تقرير الحسابات الشامل',
              subtitle: 'تقرير شامل ومفصل لجميع الحسابات المسجلة في النظام',
              date: formatter.format(now),
              reportData: {
                'totalAccounts': accounts.length,
                'totalTransactions': accounts.fold<int>(
                  0,
                  (sum, acc) => sum + acc.transactionCount,
                ),
                'totalBalance': accounts.fold<double>(
                  0,
                  (sum, acc) =>
                      sum + (acc.isPositive ? acc.balance : -acc.balance),
                ),
              },
            ),

            pw.SizedBox(height: 20),

            // Enhanced summary with stunning styling
            EnhancedTemplateHelper.createEnhancedSummary(
              template: template,
              title: 'ملخص الحسابات التفصيلي',
              summaryItems: [
                '📊 إجمالي الحسابات الدائنة: ${accounts.where((a) => a.isPositive).length} حساب',
                '📉 إجمالي الحسابات المدينة: ${accounts.where((a) => !a.isPositive).length} حساب',
                '💰 إجمالي الأرصدة الدائنة: ${accounts.where((a) => a.isPositive).fold(0.0, (sum, a) => sum + a.balance).toStringAsFixed(2)} ر.س',
                '💸 إجمالي الأرصدة المدينة: ${accounts.where((a) => !a.isPositive).fold(0.0, (sum, a) => sum + a.balance).toStringAsFixed(2)} ر.س',
                '🔢 إجمالي المعاملات: ${accounts.fold<int>(0, (sum, acc) => sum + acc.transactionCount)} معاملة',
              ],
            ),

            pw.SizedBox(height: 20),

            // Stunning enhanced table with barcodes
            if (accounts.isNotEmpty)
              EnhancedTemplateHelper.createEnhancedTable(
                template: template,
                headers: [
                  '🏷️ اسم الحساب',
                  '📞 رقم الهاتف',
                  '📍 العنوان',
                  '💰 الرصيد',
                  '📊 النوع',
                  '🔢 المعاملات',
                  '📅 تاريخ الإنشاء',
                ],
                data:
                    accounts
                        .map(
                          (account) => [
                            account.name,
                            account.phoneNumber ?? '-',
                            account.address ?? '-',
                            '${account.formattedBalance} ر.س',
                            account.typeInArabic,
                            account.transactionCount.toString(),
                            account.formattedCreatedDate,
                          ],
                        )
                        .toList(),
                boldColumns: [true, false, false, true, true, false, false],
                accounts: accounts,
                showBarcodes: true,
              )
            else
              TemplatePdfHelper.createTemplateEmptyState(
                template: template,
                message: 'لا توجد حسابات لعرضها',
              ),

            pw.SizedBox(height: 20),

            // Enhanced Footer with company info
            TemplatePdfHelper.createTemplateFooter(
              template: template,
              footerText:
                  'تم إنشاء هذا التقرير بواسطة نظام إدارة الحسابات العصري',
              companyName: 'نظام إدارة الحسابات المتقدم',
              contactInfo:
                  'القالب المستخدم: ${template.name} | تقرير شامل ومفصل',
            ),
          ];
        },
      ),
    );

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/accounts_report_${DateFormat('yyyyMMdd_HHmmss').format(now)}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  // Generate Account Statement PDF with Arabic support
  Future<File> generateAccountStatementPDF(Account account) async {
    // Initialize fonts
    await TemplatePdfHelper.initializeFonts();

    final pdf = pw.Document();
    final transactions = await _storage.getTransactionsByAccount(account.id);
    final template = await _templateService.getSelectedTemplate();
    final now = DateTime.now();
    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // Stunning enhanced header with account barcode
            EnhancedTemplateHelper.createStunningHeader(
              template: template,
              title: 'كشف حساب مفصل',
              subtitle: 'كشف حساب شامل ومحدث لجميع المعاملات والأرصدة',
              date: formatter.format(now),
              account: account,
            ),

            pw.SizedBox(height: 20),

            // Enhanced account info with stunning styling
            EnhancedTemplateHelper.createEnhancedSummary(
              template: template,
              title: 'معلومات الحساب التفصيلية',
              summaryItems: [
                '👤 اسم الحساب: ${account.name}',
                if (account.phoneNumber != null &&
                    account.phoneNumber!.isNotEmpty)
                  '📞 رقم الهاتف: ${account.phoneNumber}',
                if (account.address != null && account.address!.isNotEmpty)
                  '📍 العنوان: ${account.address}',
                '📊 نوع الحساب: ${account.typeInArabic}',
                '💰 الرصيد الحالي: ${account.formattedBalance} ر.س',
                '🔢 عدد المعاملات: ${transactions.length} معاملة',
                '📅 تاريخ الإنشاء: ${account.formattedCreatedDate}',
                if (account.notes != null && account.notes!.isNotEmpty)
                  '📝 ملاحظات: ${account.notes}',
              ],
            ),

            pw.SizedBox(height: 20),

            // Enhanced transaction summary with stunning styling
            EnhancedTemplateHelper.createEnhancedSummary(
              template: template,
              title: 'ملخص المعاملات التفصيلي',
              summaryItems: [
                '📈 إجمالي الدائن: ${transactions.where((t) => t.type.isPositive).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
                '📉 إجمالي المدين: ${transactions.where((t) => !t.type.isPositive).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
                '🔢 عدد المعاملات الدائنة: ${transactions.where((t) => t.type.isPositive).length} معاملة',
                '🔢 عدد المعاملات المدينة: ${transactions.where((t) => !t.type.isPositive).length} معاملة',
              ],
            ),

            pw.SizedBox(height: 20),

            // Stunning enhanced transactions table with barcodes
            if (transactions.isNotEmpty)
              EnhancedTemplateHelper.createEnhancedTable(
                template: template,
                headers: [
                  '📅 التاريخ',
                  '📝 الوصف',
                  '💰 المبلغ',
                  '📊 النوع',
                  '📋 ملاحظات',
                ],
                data:
                    transactions
                        .map(
                          (transaction) => [
                            transaction.formattedDate,
                            transaction.description,
                            '${transaction.formattedAmount} ر.س',
                            transaction.type.isPositive ? '📈 دائن' : '📉 مدين',
                            transaction.notes ?? '-',
                          ],
                        )
                        .toList(),
                boldColumns: [false, false, true, true, false],
                transactions: transactions,
                accounts: List.filled(transactions.length, account),
                showBarcodes: true,
              )
            else
              TemplatePdfHelper.createTemplateEmptyState(
                template: template,
                message: 'لا توجد معاملات لهذا الحساب',
              ),

            pw.SizedBox(height: 20),

            // Enhanced Footer for account statement
            TemplatePdfHelper.createTemplateFooter(
              template: template,
              footerText: 'كشف حساب مفصل ومحدث',
              companyName: 'نظام إدارة الحسابات العصري',
              contactInfo: 'حساب: ${account.name} | القالب: ${template.name}',
            ),
          ];
        },
      ),
    );

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/account_statement_${account.name}_${DateFormat('yyyyMMdd_HHmmss').format(now)}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  // Generate Excel Report (same as original)
  Future<File> generateAccountsExcelReport() async {
    final excel = Excel.createExcel();
    final sheet = excel['الحسابات'];
    final accounts = await _storage.getAllAccounts();
    final now = DateTime.now();

    // Set RTL direction
    sheet.isRTL = true;

    // Header styling
    final headerStyle = CellStyle(
      backgroundColorHex: ExcelColor.blue,
      fontColorHex: ExcelColor.white,
      bold: true,
      fontSize: 12,
    );

    // Add headers
    final headers = [
      'اسم الحساب',
      'رقم الهاتف',
      'العنوان',
      'الرصيد',
      'النوع',
      'عدد المعاملات',
      'تاريخ الإنشاء',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = headerStyle;
    }

    // Add data
    for (int i = 0; i < accounts.length; i++) {
      final account = accounts[i];
      final rowIndex = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(account.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue(account.phoneNumber ?? '-');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = TextCellValue(account.address ?? '-');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = TextCellValue('${account.formattedBalance} ر.س');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = TextCellValue(account.typeInArabic);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: rowIndex))
          .value = IntCellValue(account.transactionCount);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: rowIndex))
          .value = TextCellValue(account.formattedCreatedDate);

      // Color coding for balance
      final balanceCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex),
      );
      balanceCell.cellStyle = CellStyle(
        fontColorHex: account.isPositive ? ExcelColor.green : ExcelColor.red,
        bold: true,
      );
    }

    // Auto-fit columns
    for (int i = 0; i < headers.length; i++) {
      sheet.setColumnAutoFit(i);
    }

    final output = await getApplicationDocumentsDirectory();
    final file = File(
      '${output.path}/accounts_report_${DateFormat('yyyyMMdd_HHmmss').format(now)}.xlsx',
    );
    final bytes = excel.save();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    return file;
  }

  // Share file
  Future<void> shareFile(File file, String title) async {
    await Share.shareXFiles([XFile(file.path)], text: title);
  }

  // Generate All Reports (PDF + Excel)
  Future<List<File>> generateAllAccountsReports() async {
    final pdfFile = await generateAccountsPDFReport();
    final excelFile = await generateAccountsExcelReport();
    return [pdfFile, excelFile];
  }

  // Generate Account Statement (PDF only)
  Future<List<File>> generateAccountStatementReports(Account account) async {
    final pdfFile = await generateAccountStatementPDF(account);
    return [pdfFile];
  }
}
