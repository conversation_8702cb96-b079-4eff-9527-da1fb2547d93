import 'package:flutter/material.dart';
import '../models/account.dart';
import '../database/database_helper.dart';
import '../services/arabic_report_service.dart';

import '../widgets/modern_button.dart' as modern;
import '../theme/app_theme.dart';
import '../services/responsive_service.dart';
import '../widgets/responsive_layout.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final DatabaseHelper _storage = DatabaseHelper();
  final ArabicReportService _reportService = ArabicReportService();
  List<Account> accounts = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      isLoading = true;
    });

    try {
      final loadedAccounts = await _storage.getAllAccounts();
      setState(() {
        accounts = loadedAccounts;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  Future<void> _generateAllAccountsReport(String format) async {
    try {
      _showLoadingDialog('جاري إنشاء التقرير...');

      if (format == 'PDF') {
        final file = await _reportService.generateAccountsPDFReport();
        Navigator.pop(context);
        await _reportService.shareFile(file, 'تقرير الحسابات الشامل');
      } else if (format == 'Excel') {
        final file = await _reportService.generateAccountsExcelReport();
        Navigator.pop(context);
        await _reportService.shareFile(file, 'تقرير الحسابات الشامل');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء التقرير بصيغة $format بنجاح'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      Navigator.pop(context);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التقرير: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  Future<void> _generateAccountStatement(Account account) async {
    try {
      _showLoadingDialog('جاري إنشاء كشف الحساب...');

      final file = await _reportService.generateAccountStatementPDF(account);
      Navigator.pop(context);
      await _reportService.shareFile(file, 'كشف حساب ${account.name}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء كشف حساب ${account.name} بنجاح'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      Navigator.pop(context);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء كشف الحساب: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: Theme.of(context).primaryColor),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            title: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF6A1B9A), // Deep purple
                    const Color(0xFF8E24AA), // Purple
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6A1B9A).withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: ResponsiveText(
                'التقارير',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            centerTitle: true,
            leading: Container(
              margin: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFD32F2F), // Dark red
                    const Color(0xFFE53935), // Red
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFD32F2F).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back_rounded,
                  color: Colors.white,
                  size: 22,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors:
                    Theme.of(context).brightness == Brightness.dark
                        ? [
                          AppTheme.darkBackgroundColor,
                          AppTheme.darkSurfaceColor,
                        ]
                        : [const Color(0xFFF8F9FA), const Color(0xFFE9ECEF)],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                padding: ResponsiveService.getResponsivePadding(context),
                child: Column(
                  children: [
                    const SizedBox(height: 20),

                    // Header Card
                    ResponsiveCard(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: ResponsiveService.getResponsivePadding(
                          context,
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: ResponsiveIcon(
                                Icons.assessment_rounded,
                                size: 24,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ResponsiveText(
                                    'التقارير والكشوفات',
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                  ResponsiveText(
                                    'إنشاء تقارير PDF و Excel',
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.9),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    ResponsiveSizedBox.height(24),

                    // General Reports Section
                    ResponsiveCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveText(
                            'التقارير العامة',
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color:
                                Theme.of(context).textTheme.titleLarge?.color,
                          ),
                          ResponsiveSizedBox.height(16),

                          // All Accounts Report
                          _buildReportOption(
                            title: 'تقرير جميع الحسابات',
                            subtitle: 'تقرير شامل لجميع الحسابات والأرصدة',
                            icon: Icons.account_balance_rounded,
                            gradient: AppTheme.primaryGradient,
                            onPDFTap: () => _generateAllAccountsReport('PDF'),
                            onExcelTap:
                                () => _generateAllAccountsReport('Excel'),
                          ),
                        ],
                      ),
                    ),

                    ResponsiveSizedBox.height(24),

                    // Account Statements Section
                    if (accounts.isNotEmpty) ...[
                      ResponsiveCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ResponsiveText(
                              'كشوفات الحسابات',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.titleLarge?.color,
                            ),
                            ResponsiveSizedBox.height(16),

                            ...accounts.map(
                              (account) => Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: _buildAccountStatementOption(account),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ] else if (!isLoading) ...[
                      ResponsiveCard(
                        child: Column(
                          children: [
                            ResponsiveIcon(
                              Icons.folder_open_rounded,
                              size: 48,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                            ResponsiveSizedBox.height(16),
                            ResponsiveText(
                              'لا توجد حسابات',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                            ResponsiveSizedBox.height(8),
                            ResponsiveText(
                              'أضف حسابات لإنشاء كشوفات الحسابات',
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],

                    if (isLoading) ...[
                      const SizedBox(height: 50),
                      const Center(child: CircularProgressIndicator()),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReportOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required LinearGradient gradient,
    required VoidCallback onPDFTap,
    required VoidCallback onExcelTap,
  }) {
    return Container(
      padding: ResponsiveService.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(
          ResponsiveService.getResponsiveBorderRadius(context, 12),
        ),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ResponsiveIcon(icon, size: 20, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      title,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleMedium?.color,
                    ),
                    const SizedBox(height: 4),
                    ResponsiveText(
                      subtitle,
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ],
                ),
              ),
            ],
          ),
          ResponsiveSizedBox.height(16),
          Row(
            children: [
              Expanded(
                child: modern.ModernButton(
                  text: 'PDF',
                  icon: Icons.picture_as_pdf_rounded,
                  onPressed: onPDFTap,
                  gradient: AppTheme.errorGradient,
                  height: ResponsiveService.getResponsiveButtonHeight(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: modern.ModernButton(
                  text: 'Excel',
                  icon: Icons.table_chart_rounded,
                  onPressed: onExcelTap,
                  gradient: AppTheme.successGradient,
                  height: ResponsiveService.getResponsiveButtonHeight(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStatementOption(Account account) {
    return Container(
      padding: ResponsiveService.getResponsivePadding(context),
      decoration: BoxDecoration(
        color:
            account.isPositive
                ? AppTheme.successColor.withValues(alpha: 0.05)
                : AppTheme.errorColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(
          ResponsiveService.getResponsiveBorderRadius(context, 12),
        ),
        border: Border.all(
          color:
              account.isPositive
                  ? AppTheme.successColor.withValues(alpha: 0.3)
                  : AppTheme.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient:
                      account.isPositive
                          ? AppTheme.successGradient
                          : AppTheme.errorGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ResponsiveIcon(
                  account.isPositive
                      ? Icons.trending_up_rounded
                      : Icons.trending_down_rounded,
                  size: 20,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      account.name,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        ResponsiveText(
                          '${account.formattedBalance} ر.س',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color:
                              account.isPositive
                                  ? AppTheme.successColor
                                  : AppTheme.errorColor,
                        ),
                        const SizedBox(width: 8),
                        ResponsiveText(
                          '• ${account.transactionCount} معاملة',
                          fontSize: 12,
                          color: AppTheme.textSecondary,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          ResponsiveSizedBox.height(16),
          SizedBox(
            width: double.infinity,
            child: modern.ModernButton(
              text: 'إنشاء كشف الحساب',
              icon: Icons.description_rounded,
              onPressed: () => _generateAccountStatement(account),
              gradient:
                  account.isPositive
                      ? AppTheme.successGradient
                      : AppTheme.errorGradient,
              height: ResponsiveService.getResponsiveButtonHeight(context),
            ),
          ),
        ],
      ),
    );
  }
}
