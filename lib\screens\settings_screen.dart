import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../database/database_helper.dart';
import '../services/settings_manager.dart';
import '../services/security_manager.dart';
import '../services/backup_manager.dart';
import '../services/theme_service.dart';
import '../services/language_service.dart';
import '../services/notification_service.dart';
import '../models/app_settings.dart';
import '../widgets/settings_widgets.dart';
import '../widgets/settings_dialogs.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final DatabaseHelper _storage = DatabaseHelper();
  final SettingsManager _settingsManager = SettingsManager();
  final SecurityManager _securityManager = SecurityManager();
  final BackupManager _backupManager = BackupManager();

  AppSettings _settings = const AppSettings();
  bool _isLoading = false;
  BackupStatistics? _backupStats;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadBackupStatistics();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _settingsManager.init();
      setState(() {
        _settings = _settingsManager.settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadBackupStatistics() async {
    try {
      final stats = await _backupManager.getBackupStatistics();
      setState(() {
        _backupStats = stats;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _updateSettings(AppSettings newSettings) async {
    setState(() {
      _settings = newSettings;
    });
    await _settingsManager.updateSettings(newSettings);

    // Update all services with new settings using Provider
    if (mounted) {
      final themeService = Provider.of<ThemeService>(context, listen: false);
      final languageService = Provider.of<LanguageService>(
        context,
        listen: false,
      );
      final notificationService = Provider.of<NotificationService>(
        context,
        listen: false,
      );

      themeService.updateTheme(
        theme: newSettings.appTheme,
        darkMode: newSettings.darkModeEnabled,
        fontSize: newSettings.fontSize,
      );

      languageService.updateLanguage(newSettings.selectedLanguage);
      notificationService.updateSettings(newSettings);

      // Show notification about settings change
      if (newSettings.notificationsEnabled) {
        await notificationService.showNotification(
          title: languageService.getString('settings'),
          body: 'تم تحديث الإعدادات بنجاح',
          type: NotificationType.success,
        );
      }
    }

    // Log settings change
    _securityManager.logSecurityEvent(
      SecurityEventType.settingsChange,
      'تم تحديث الإعدادات',
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: const Color(0xFF4A5FC7),
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: const Color(0xFF4A5FC7),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showAppInfo(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Statistics Card
            if (_backupStats != null) _buildStatisticsCard(),

            const SizedBox(height: 16),

            // General Settings Section
            _buildGeneralSettings(),

            const SizedBox(height: 16),

            // Financial Settings Section
            _buildFinancialSettings(),

            const SizedBox(height: 16),

            // Backup Settings Section
            _buildBackupSettings(),

            const SizedBox(height: 16),

            // Security Settings Section
            _buildSecuritySettings(),

            const SizedBox(height: 16),

            // UI Settings Section
            _buildUISettings(),

            const SizedBox(height: 16),

            // Advanced Settings Section
            _buildAdvancedSettings(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'إحصائيات التطبيق',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4A5FC7),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SettingsWidgets.buildStatCard(
                title: 'الحسابات',
                value: _backupStats!.totalAccounts.toString(),
                icon: Icons.account_balance_wallet,
                color: Colors.blue,
              ),
              SettingsWidgets.buildStatCard(
                title: 'المعاملات',
                value: _backupStats!.totalTransactions.toString(),
                icon: Icons.receipt_long,
                color: Colors.green,
              ),
              SettingsWidgets.buildStatCard(
                title: 'حجم البيانات',
                value: _backupStats!.formattedBackupSize,
                icon: Icons.storage,
                color: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'الإعدادات العامة',
      icon: Icons.settings,
      children: [
        SettingsWidgets.buildSwitchTile(
          icon: Icons.notifications,
          title: 'الإشعارات',
          subtitle: 'تفعيل إشعارات التطبيق',
          value: _settings.notificationsEnabled,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(notificationsEnabled: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.dark_mode,
          title: 'الوضع المظلم',
          subtitle: 'تفعيل الوضع المظلم',
          value: _settings.darkModeEnabled,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(darkModeEnabled: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.language,
          title: 'اللغة',
          subtitle: _settings.selectedLanguage,
          items: _settingsManager.getAvailableLanguages(),
          onChanged: (value) {
            _updateSettings(_settings.copyWith(selectedLanguage: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.volume_up,
          title: 'الأصوات',
          subtitle: 'تفعيل أصوات التطبيق',
          value: _settings.soundEnabled,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(soundEnabled: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.vibration,
          title: 'الاهتزاز',
          subtitle: 'تفعيل الاهتزاز',
          value: _settings.vibrationEnabled,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(vibrationEnabled: value));
          },
        ),
      ],
    );
  }

  Widget _buildFinancialSettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'الإعدادات المالية',
      icon: Icons.account_balance_wallet,
      children: [
        SettingsWidgets.buildDropdownTile(
          icon: Icons.attach_money,
          title: 'العملة الافتراضية',
          subtitle: _settings.selectedCurrency,
          items: _settingsManager.getAvailableCurrencies(),
          onChanged: (value) {
            _updateSettings(_settings.copyWith(selectedCurrency: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.format_list_numbered,
          title: 'تنسيق الأرقام',
          subtitle: _settings.numberFormat,
          items: ['1,234.56', '1.234,56', '1 234.56'],
          onChanged: (value) {
            _updateSettings(_settings.copyWith(numberFormat: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.date_range,
          title: 'تنسيق التاريخ',
          subtitle: _settings.dateFormat,
          items: ['dd/MM/yyyy', 'MM/dd/yyyy', 'yyyy-MM-dd', 'dd-MM-yyyy'],
          onChanged: (value) {
            _updateSettings(_settings.copyWith(dateFormat: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.access_time,
          title: 'تنسيق الوقت',
          subtitle: _settings.timeFormat,
          items: ['24h', '12h'],
          onChanged: (value) {
            _updateSettings(_settings.copyWith(timeFormat: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.home,
          title: 'عرض الرصيد في الصفحة الرئيسية',
          subtitle: 'إظهار الرصيد الإجمالي',
          value: _settings.showBalanceOnHome,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(showBalanceOnHome: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.warning,
          title: 'تأكيد قبل الحذف',
          subtitle: 'طلب تأكيد قبل حذف البيانات',
          value: _settings.confirmBeforeDelete,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(confirmBeforeDelete: value));
          },
        ),
      ],
    );
  }

  Widget _buildBackupSettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'النسخ الاحتياطي',
      icon: Icons.backup,
      children: [
        SettingsWidgets.buildSwitchTile(
          icon: Icons.cloud_sync,
          title: 'النسخ الاحتياطي التلقائي',
          subtitle: 'نسخ احتياطي تلقائي حسب الجدولة',
          value: _settings.autoBackupEnabled,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(autoBackupEnabled: value));
            if (value) {
              _backupManager.enableAutoBackup();
            } else {
              _backupManager.disableAutoBackup();
            }
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.schedule,
          title: 'وقت النسخ الاحتياطي',
          subtitle: _settings.backupTime,
          onTap:
              () => SettingsDialogs.showTimePickerDialog(
                context,
                _settings,
                _updateSettings,
              ),
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.repeat,
          title: 'تكرار النسخ الاحتياطي',
          subtitle: _settings.backupFrequency.toString(),
          items: ['1', '3', '7', '14', '30'],
          onChanged: (value) {
            _updateSettings(
              _settings.copyWith(backupFrequency: int.parse(value!)),
            );
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.cloud_download,
          title: 'استيراد إعدادات',
          subtitle: 'استيراد إعدادات من ملف',
          onTap: () => SettingsDialogs.showImportDialog(context),
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.cloud_upload,
          title: 'تصدير إعدادات',
          subtitle: 'تصدير الإعدادات الحالية',
          onTap:
              () => SettingsDialogs.showExportDialog(context, _settingsManager),
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.backup_table,
          title: 'إنشاء نسخة احتياطية الآن',
          subtitle: 'إنشاء نسخة احتياطية فورية',
          onTap: () => _createBackupNow(),
        ),
        if (_backupStats?.lastBackupTime != null) ...[
          const Divider(height: 1),
          SettingsWidgets.buildInfoTile(
            icon: Icons.history,
            title: 'آخر نسخة احتياطية',
            subtitle: _formatLastBackupTime(_backupStats!.lastBackupTime!),
          ),
        ],
      ],
    );
  }

  Widget _buildSecuritySettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'الأمان والخصوصية',
      icon: Icons.security,
      children: [
        SettingsWidgets.buildSwitchTile(
          icon: Icons.lock,
          title: 'حماية بكلمة مرور',
          subtitle: 'تفعيل حماية التطبيق بكلمة مرور',
          value: _settings.passwordEnabled,
          onChanged: (value) {
            if (value) {
              SettingsDialogs.showPasswordSetupDialog(
                context,
                _settings,
                _settingsManager,
                _updateSettings,
              );
            } else {
              _updateSettings(
                _settings.copyWith(
                  passwordEnabled: false,
                  passwordHash: '',
                  biometricEnabled: false,
                ),
              );
            }
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildSwitchTile(
          icon: Icons.fingerprint,
          title: 'البصمة الحيوية',
          subtitle: 'تسجيل الدخول بالبصمة',
          value: _settings.biometricEnabled,
          onChanged:
              _settings.passwordEnabled
                  ? (value) {
                    _updateSettings(
                      _settings.copyWith(biometricEnabled: value),
                    );
                  }
                  : null,
        ),
        if (_settings.passwordEnabled) ...[
          const Divider(height: 1),
          SettingsWidgets.buildTile(
            icon: Icons.edit,
            title: 'تغيير كلمة المرور',
            subtitle: 'تحديث كلمة المرور الحالية',
            onTap:
                () => SettingsDialogs.showPasswordChangeDialog(
                  context,
                  _settings,
                  _settingsManager,
                  _updateSettings,
                ),
          ),
        ],
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.security,
          title: 'سجل الأمان',
          subtitle: 'عرض سجل الأنشطة الأمنية',
          onTap: () => _showSecurityLog(),
        ),
      ],
    );
  }

  Widget _buildUISettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'واجهة المستخدم',
      icon: Icons.palette,
      children: [
        SettingsWidgets.buildSliderTile(
          icon: Icons.text_fields,
          title: 'حجم الخط',
          subtitle: '${_settings.fontSize.toInt()}',
          value: _settings.fontSize,
          min: 12.0,
          max: 24.0,
          onChanged: (value) {
            _updateSettings(_settings.copyWith(fontSize: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.color_lens,
          title: 'سمة التطبيق',
          subtitle: _settings.appTheme,
          items: _settingsManager.getAvailableThemes(),
          onChanged: (value) {
            _updateSettings(_settings.copyWith(appTheme: value));
          },
        ),
        const Divider(height: 1),
        SettingsWidgets.buildDropdownTile(
          icon: Icons.credit_card,
          title: 'نوع المعاملة الافتراضي',
          subtitle:
              _settings.defaultTransactionType == 'credit' ? 'دائن' : 'مدين',
          items: ['دائن', 'مدين'],
          onChanged: (value) {
            final type = value == 'دائن' ? 'credit' : 'debit';
            _updateSettings(_settings.copyWith(defaultTransactionType: type));
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return SettingsWidgets.buildSectionCard(
      title: 'إعدادات متقدمة',
      icon: Icons.tune,
      children: [
        SettingsWidgets.buildTile(
          icon: Icons.delete_forever,
          title: 'مسح جميع البيانات',
          subtitle: 'حذف جميع الحسابات والمعاملات',
          onTap:
              () => SettingsDialogs.showClearDataDialog(
                context,
                _storage,
                () => _loadBackupStatistics(),
              ),
          textColor: Colors.red,
          iconColor: Colors.red,
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.refresh,
          title: 'إعادة تعيين الإعدادات',
          subtitle: 'استعادة الإعدادات الافتراضية',
          onTap:
              () => SettingsDialogs.showResetSettingsDialog(
                context,
                _settingsManager,
                () => _loadSettings(),
              ),
          textColor: Colors.orange,
          iconColor: Colors.orange,
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.bug_report,
          title: 'تقرير مشكلة',
          subtitle: 'إرسال تقرير عن مشكلة في التطبيق',
          onTap: () => _showBugReport(),
        ),
        const Divider(height: 1),
        SettingsWidgets.buildTile(
          icon: Icons.rate_review,
          title: 'تقييم التطبيق',
          subtitle: 'تقييم التطبيق في المتجر',
          onTap: () => _showRateApp(),
        ),
      ],
    );
  }

  // Helper methods
  Future<void> _createBackupNow() async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('جاري إنشاء النسخة الاحتياطية...'),
                ],
              ),
            ),
      );

      await _backupManager.createFullBackup();
      await _loadBackupStatistics();

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // Show backup notification
        final notificationService = Provider.of<NotificationService>(
          context,
          listen: false,
        );
        await notificationService.showBackupNotification(
          success: true,
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء النسخة الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );

        // Show backup failure notification
        final notificationService = Provider.of<NotificationService>(
          context,
          listen: false,
        );
        await notificationService.showBackupNotification(
          success: false,
          message: 'فشل في إنشاء النسخة الاحتياطية: $e',
        );
      }
    }
  }

  String _formatLastBackupTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showSecurityLog() {
    final securityLog = _securityManager.getSecurityLog();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('سجل الأمان'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child:
                  securityLog.isEmpty
                      ? const Center(child: Text('لا توجد أحداث أمنية'))
                      : ListView.builder(
                        itemCount: securityLog.length,
                        itemBuilder: (context, index) {
                          final event =
                              securityLog[securityLog.length - 1 - index];
                          return ListTile(
                            leading: Icon(_getSecurityEventIcon(event.type)),
                            title: Text(event.description),
                            subtitle: Text(
                              _settingsManager.formatDate(event.timestamp),
                            ),
                          );
                        },
                      ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              if (securityLog.isNotEmpty)
                TextButton(
                  onPressed: () {
                    _securityManager.clearSecurityLog();
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم مسح سجل الأمان')),
                    );
                  },
                  child: const Text('مسح السجل'),
                ),
            ],
          ),
    );
  }

  IconData _getSecurityEventIcon(SecurityEventType type) {
    switch (type) {
      case SecurityEventType.login:
        return Icons.login;
      case SecurityEventType.logout:
        return Icons.logout;
      case SecurityEventType.failedLogin:
        return Icons.error;
      case SecurityEventType.passwordChange:
        return Icons.lock;
      case SecurityEventType.settingsChange:
        return Icons.settings;
      case SecurityEventType.dataExport:
        return Icons.upload;
      case SecurityEventType.dataImport:
        return Icons.download;
      case SecurityEventType.dataDelete:
        return Icons.delete;
    }
  }

  void _showAppInfo() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('معلومات التطبيق'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('اسم التطبيق: تطبيق المحاسبة'),
                Text('الإصدار: 1.0.0'),
                Text('المطور: فريق التطوير'),
                SizedBox(height: 16),
                Text('تطبيق شامل لإدارة الحسابات والمعاملات المالية'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showBugReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح نموذج تقرير المشاكل قريباً')),
    );
  }

  void _showRateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح صفحة التقييم قريباً')),
    );
  }
}
