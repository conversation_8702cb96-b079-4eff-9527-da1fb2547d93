import 'package:flutter/material.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';
import '../widgets/transaction_item.dart';
import '../theme/app_theme.dart';
import '../services/custom_currency_service.dart';
import '../models/currency.dart';
import 'add_amount_screen.dart';
import 'edit_transaction_screen.dart';
import 'account_options_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  final DatabaseHelper _storage = DatabaseHelper();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  List<Transaction> transactions = [];
  bool isLoading = true;
  Currency? _accountCurrency;
  Account? currentAccount;

  @override
  void initState() {
    super.initState();
    currentAccount = widget.account;
    _loadAccountCurrency();
    _loadTransactions();
  }

  Future<void> _loadAccountCurrency() async {
    if (widget.account.currencyId != null) {
      final currency = await _currencyService.getCurrencyById(
        widget.account.currencyId!,
      );
      if (mounted) {
        setState(() {
          _accountCurrency = currency;
        });
      }
    } else {
      final defaultCurrency = await _currencyService.getDefaultCurrency();
      if (mounted) {
        setState(() {
          _accountCurrency = defaultCurrency;
        });
      }
    }
  }

  Future<void> _loadTransactions() async {
    setState(() {
      isLoading = true;
    });

    try {
      final loadedTransactions = await _storage.getTransactionsByAccount(
        widget.account.id,
      );
      final updatedAccount = await _storage.getAccount(widget.account.id);

      if (mounted) {
        setState(() {
          transactions = loadedTransactions;
          currentAccount = updatedAccount;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _addTransaction() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddAmountScreen(account: currentAccount!),
      ),
    );

    if (result == true) {
      await _loadTransactions();
    }
  }

  Future<void> _editTransaction(Transaction transaction) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => EditTransactionScreen(
              transaction: transaction,
              account: currentAccount!,
            ),
      ),
    );

    if (result == true) {
      await _loadTransactions();
    }
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    try {
      await _storage.deleteTransaction(transaction.id);
      await _loadTransactions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف المعاملة "${transaction.description}" بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المعاملة: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _shareAccountDetails() async {
    if (currentAccount?.phoneNumber == null ||
        currentAccount!.phoneNumber!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('رقم الهاتف غير متوفر لهذا الحساب'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // Get current currency
    final currencySymbol = _accountCurrency?.symbol ?? 'ر.س';

    // Create summary message
    final content = StringBuffer();
    content.writeln('كشف حساب: ${currentAccount!.name}');
    content.writeln(
      'الرصيد الحالي: ${currentAccount!.formattedBalanceWithCurrency(currencySymbol)}',
    );

    if (transactions.isNotEmpty) {
      content.writeln('\nآخر المعاملات:');
      final recentTransactions = transactions.take(5);
      for (final transaction in recentTransactions) {
        content.writeln(
          '${transaction.formattedDate}: ${transaction.formattedAmount} - ${transaction.description}',
        );
      }
    }

    try {
      final phoneNumber = currentAccount!.phoneNumber!;
      final message = Uri.encodeComponent(content.toString());
      final whatsappUrl = 'https://wa.me/$phoneNumber?text=$message';

      final uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم فتح واتساب لإرسال كشف الحساب'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        throw 'لا يمكن فتح واتساب';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح واتساب: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showAccountOptions() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) =>
                AccountOptionsScreen(account: currentAccount!),
        transitionDuration: const Duration(milliseconds: 300),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    ).then((result) {
      if (result == 'updated') {
        _loadTransactions(); // This will reload the account data too
      } else if (result == 'deleted' && mounted) {
        Navigator.pop(context, true); // Go back to previous screen
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (currentAccount == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF4A5FBF),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context, true),
        ),
        title: Row(
          children: [
            Icon(
              currentAccount!.isPositive
                  ? Icons.trending_up
                  : Icons.trending_down,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'كشف حساب ${currentAccount!.name}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: _showAccountOptions,
          ),
        ],
      ),
      body: Column(
        children: [
          // Account Balance Header
          Container(
            width: double.infinity,
            color: const Color(0xFF4A5FBF),
            padding: const EdgeInsets.only(bottom: 16),
            child: Center(
              child: Text(
                'الرصيد: ${_accountCurrency != null ? currentAccount!.formattedBalanceWithCurrency(_accountCurrency!.symbol) : currentAccount!.formattedBalance}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
            ),
          ),

          // Transactions List
          Expanded(
            child:
                isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : transactions.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long_rounded,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد معاملات',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'ابدأ بإضافة معاملة جديدة',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        return TransactionItem(
                          transaction: transaction,
                          onTap: () {
                            // View transaction details
                          },
                          onEdit: () => _editTransaction(transaction),
                          onDelete: () => _deleteTransaction(transaction),
                        );
                      },
                    ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 60,
        color: const Color(0xFF4A5FBF),
        child: Row(
          children: [
            // Message Icon
            Expanded(
              child: IconButton(
                icon: const Icon(Icons.mail_outline, color: Colors.white),
                onPressed: _shareAccountDetails,
              ),
            ),

            // Balance Display
            Expanded(
              flex: 3,
              child: Center(
                child: Text(
                  'الرصيد: ${_accountCurrency != null ? currentAccount!.formattedBalanceWithCurrency(_accountCurrency!.symbol) : currentAccount!.formattedBalance}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ),

            // Add Transaction Icon
            Expanded(
              child: IconButton(
                icon: const Icon(Icons.add_circle_outline, color: Colors.white),
                onPressed: _addTransaction,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
