import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../models/report_template.dart';
import '../models/transaction.dart';
import 'sample_data_service.dart';
import 'template_pdf_helper.dart';

class WebTemplatePreviewService {
  static Future<Uint8List> generatePreviewPDFBytes(
    ReportTemplate template,
  ) async {
    // Initialize fonts
    await TemplatePdfHelper.initializeFonts();

    final pdf = pw.Document();
    final sampleAccounts = SampleDataService.getSampleAccounts();
    final summary = SampleDataService.getSampleSummary();
    final now = DateTime.now();
    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    // Add accounts report page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            // Header with template styling
            TemplatePdfHelper.createTemplateHeader(
              template: template,
              title: 'معاينة تقرير الحسابات - ${template.name}',
              subtitle: 'تاريخ المعاينة: ${formatter.format(now)}',
              date: 'عدد الحسابات: ${sampleAccounts.length}',
            ),

            pw.SizedBox(height: 20),

            // Preview notice
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                color: template.pdfAccentColor,
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(
                  color: template.pdfPrimaryColor,
                  width: 1,
                ),
              ),
              child: pw.Center(
                child: pw.Text(
                  '*** هذه معاينة للقالب باستخدام بيانات تجريبية ***',
                  style: TemplatePdfHelper.fontService.getArabicTextStyle(
                    fontSize: 14,
                    bold: true,
                    color: template.pdfPrimaryColor,
                  ),
                ),
              ),
            ),

            pw.SizedBox(height: 20),

            // Summary with template styling
            TemplatePdfHelper.createTemplateSummary(
              template: template,
              title: 'ملخص الحسابات التجريبية',
              summaryItems: [
                'إجمالي الحسابات الدائنة: ${summary['positiveAccounts']}',
                'إجمالي الحسابات المدينة: ${summary['negativeAccounts']}',
                'إجمالي الأرصدة الدائنة: ${summary['totalCredit'].toStringAsFixed(2)} ر.س',
                'إجمالي الأرصدة المدينة: ${summary['totalDebit'].toStringAsFixed(2)} ر.س',
                'صافي الرصيد: ${summary['totalBalance'].toStringAsFixed(2)} ر.س',
                'إجمالي المعاملات: ${summary['totalTransactions']}',
              ],
            ),

            pw.SizedBox(height: 20),

            // Accounts Table with template styling
            TemplatePdfHelper.createTemplateTable(
              template: template,
              headers: [
                'اسم الحساب',
                'رقم الهاتف',
                'العنوان',
                'الرصيد',
                'النوع',
                'تاريخ الإنشاء',
              ],
              data:
                  sampleAccounts
                      .map(
                        (account) => [
                          account.name,
                          account.phoneNumber ?? '-',
                          account.address ?? '-',
                          '${account.formattedBalance} ر.س',
                          account.typeInArabic,
                          account.formattedCreatedDate,
                        ],
                      )
                      .toList(),
              boldColumns: [false, false, false, true, false, false],
            ),

            pw.SizedBox(height: 20),

            // Footer with template styling
            TemplatePdfHelper.createTemplateFooter(
              template: template,
              footerText:
                  'معاينة القالب: ${template.name} - بيانات تجريبية للعرض فقط',
            ),
          ];
        },
      ),
    );

    // Add account statement page
    final sampleAccount = SampleDataService.getSampleAccountForStatement();
    final accountTransactions =
        SampleDataService.getSampleTransactionsForAccount(sampleAccount.id);

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            // Header with template styling
            TemplatePdfHelper.createTemplateHeader(
              template: template,
              title: 'معاينة كشف حساب - ${template.name}',
              subtitle: 'اسم الحساب: ${sampleAccount.name}',
              date: 'تاريخ المعاينة: ${formatter.format(now)}',
            ),

            pw.SizedBox(height: 20),

            // Preview notice
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                color: template.pdfAccentColor,
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(
                  color: template.pdfPrimaryColor,
                  width: 1,
                ),
              ),
              child: pw.Center(
                child: pw.Text(
                  'معاينة كشف حساب بالقالب المختار',
                  style: TemplatePdfHelper.fontService.getArabicTextStyle(
                    fontSize: 14,
                    bold: true,
                    color: template.pdfPrimaryColor,
                  ),
                ),
              ),
            ),

            pw.SizedBox(height: 20),

            // Account Info with template styling
            TemplatePdfHelper.createTemplateSummary(
              template: template,
              title: 'معلومات الحساب التجريبي',
              summaryItems: [
                'اسم الحساب: ${sampleAccount.name}',
                'رقم الهاتف: ${sampleAccount.phoneNumber}',
                'العنوان: ${sampleAccount.address}',
                'نوع الحساب: ${sampleAccount.typeInArabic}',
                'الرصيد الحالي: ${sampleAccount.formattedBalance} ر.س',
                'عدد المعاملات: ${accountTransactions.length}',
                'تاريخ الإنشاء: ${sampleAccount.formattedCreatedDate}',
                'ملاحظات: ${sampleAccount.notes}',
              ],
            ),

            pw.SizedBox(height: 20),

            // Transaction Summary with template styling
            TemplatePdfHelper.createTemplateSummary(
              template: template,
              title: 'ملخص المعاملات التجريبية',
              summaryItems: [
                'إجمالي الدائن: ${accountTransactions.where((t) => t.type == TransactionType.credit).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
                'إجمالي المدين: ${accountTransactions.where((t) => t.type == TransactionType.debit).fold(0.0, (sum, t) => sum + t.amount).toStringAsFixed(2)} ر.س',
              ],
            ),

            pw.SizedBox(height: 20),

            // Transactions Table with template styling
            if (accountTransactions.isNotEmpty)
              TemplatePdfHelper.createTemplateTable(
                template: template,
                headers: ['التاريخ', 'الوصف', 'المبلغ', 'النوع', 'ملاحظات'],
                data:
                    accountTransactions
                        .map(
                          (transaction) => [
                            transaction.formattedDate,
                            transaction.description,
                            '${transaction.formattedAmount} ر.س',
                            transaction.type == TransactionType.credit
                                ? 'دائن'
                                : 'مدين',
                            transaction.notes ?? '-',
                          ],
                        )
                        .toList(),
                boldColumns: [false, false, true, false, false],
              )
            else
              TemplatePdfHelper.createTemplateEmptyState(
                template: template,
                message: 'لا توجد معاملات لهذا الحساب التجريبي',
              ),

            pw.SizedBox(height: 20),

            // Footer with template styling
            TemplatePdfHelper.createTemplateFooter(
              template: template,
              footerText:
                  'معاينة كشف حساب ${sampleAccount.name} - القالب: ${template.name}',
            ),
          ];
        },
      ),
    );

    // Return PDF bytes
    return await pdf.save();
  }

  static Future<void> downloadPreviewPDF(ReportTemplate template) async {
    if (!kIsWeb) return;

    try {
      final pdfBytes = await generatePreviewPDFBytes(template);
      final fileName =
          'template_preview_${template.name}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      // For web, trigger download
      // This would need web-specific implementation
      // For now, we'll just show a message
      print('PDF generated successfully: $fileName');
    } catch (e) {
      print('Error generating PDF: $e');
      rethrow;
    }
  }
}
